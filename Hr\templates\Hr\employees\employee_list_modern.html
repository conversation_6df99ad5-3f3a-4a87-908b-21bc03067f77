{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}قائمة الموظفين{% endblock %}
{% block page_title %}قائمة الموظفين{% endblock %}

{% block breadcrumb_items %}
<div class="breadcrumb-item">
    <span class="breadcrumb-link">الموظفون</span>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .employee-card {
        transition: all var(--transition-fast);
        cursor: pointer;
    }
    
    .employee-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .employee-avatar {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
    }
    
    .employee-status {
        position: absolute;
        top: var(--space-2);
        left: var(--space-2);
    }
    
    .filters-panel {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-sm);
    }
    
    .view-toggle {
        display: flex;
        background: var(--neutral-100);
        border-radius: var(--radius-md);
        padding: var(--space-1);
    }
    
    .view-toggle button {
        padding: var(--space-2) var(--space-4);
        border: none;
        background: transparent;
        border-radius: var(--radius-sm);
        cursor: pointer;
        transition: all var(--transition-fast);
    }
    
    .view-toggle button.active {
        background: white;
        box-shadow: var(--shadow-sm);
        color: var(--primary-600);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }
    
    .stat-card {
        background: white;
        padding: var(--space-6);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        text-align: center;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-3);
        font-size: var(--text-xl);
    }
    
    .employee-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--space-4);
    }
    
    @media (max-width: 768px) {
        .employee-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon bg-primary text-white">
            <i class="fas fa-users"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-800 mb-1">{{ total_employees }}</div>
        <div class="text-sm text-neutral-600">إجمالي الموظفين</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon bg-success text-white">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-800 mb-1">{{ active_employees }}</div>
        <div class="text-sm text-neutral-600">الموظفون النشطون</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon bg-warning text-white">
            <i class="fas fa-user-plus"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-800 mb-1">{{ new_employees_month }}</div>
        <div class="text-sm text-neutral-600">موظفون جدد هذا الشهر</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon bg-error text-white">
            <i class="fas fa-user-times"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-800 mb-1">{{ inactive_employees }}</div>
        <div class="text-sm text-neutral-600">موظفون غير نشطون</div>
    </div>
</div>

<!-- Filters and Controls -->
<div class="filters-panel">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <h2 class="text-xl font-semibold text-neutral-800">إدارة الموظفين</h2>
        
        <div class="flex items-center gap-3">
            <!-- View Toggle -->
            <div class="view-toggle">
                <button type="button" id="grid-view" class="active">
                    <i class="fas fa-th-large"></i>
                </button>
                <button type="button" id="table-view">
                    <i class="fas fa-list"></i>
                </button>
            </div>
            
            <!-- Add Employee Button -->
            <a href="{% url 'Hr:employee_add' %}" class="btn btn-primary">
                <i class="fas fa-plus ml-2"></i>
                إضافة موظف
            </a>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Search -->
        <div class="form-group">
            <input type="text" id="employee-search" class="form-input" 
                   placeholder="البحث عن موظف...">
        </div>
        
        <!-- Department Filter -->
        <div class="form-group">
            <select id="department-filter" class="form-select">
                <option value="">جميع الأقسام</option>
                {% for department in departments %}
                    <option value="{{ department.id }}">{{ department.name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <!-- Status Filter -->
        <div class="form-group">
            <select id="status-filter" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
                <option value="on_leave">في إجازة</option>
                <option value="terminated">منتهي الخدمة</option>
            </select>
        </div>
        
        <!-- Employment Type Filter -->
        <div class="form-group">
            <select id="employment-type-filter" class="form-select">
                <option value="">جميع أنواع التوظيف</option>
                <option value="full_time">دوام كامل</option>
                <option value="part_time">دوام جزئي</option>
                <option value="contract">تعاقد</option>
                <option value="temporary">مؤقت</option>
            </select>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="flex flex-wrap gap-2 mt-4">
        <button class="btn btn-outline btn-sm" id="export-excel">
            <i class="fas fa-file-excel ml-2"></i>
            تصدير Excel
        </button>
        <button class="btn btn-outline btn-sm" id="export-pdf">
            <i class="fas fa-file-pdf ml-2"></i>
            تصدير PDF
        </button>
        <button class="btn btn-outline btn-sm" id="bulk-actions">
            <i class="fas fa-tasks ml-2"></i>
            إجراءات مجمعة
        </button>
    </div>
</div>

<!-- Employees Grid View -->
<div id="employees-grid" class="employee-grid">
    {% for employee in employees %}
    <div class="employee-card card relative" data-employee-id="{{ employee.id }}">
        <!-- Status Badge -->
        <div class="employee-status">
            {% if employee.status == 'active' %}
                <span class="badge badge-success">نشط</span>
            {% elif employee.status == 'inactive' %}
                <span class="badge badge-secondary">غير نشط</span>
            {% elif employee.status == 'on_leave' %}
                <span class="badge badge-warning">في إجازة</span>
            {% elif employee.status == 'terminated' %}
                <span class="badge badge-error">منتهي الخدمة</span>
            {% endif %}
        </div>
        
        <div class="card-body text-center">
            <!-- Avatar -->
            <div class="employee-avatar mx-auto mb-4">
                {% if employee.profile_picture %}
                    <img src="{{ employee.profile_picture.url }}" alt="{{ employee.full_name }}" 
                         class="w-full h-full object-cover rounded-full">
                {% else %}
                    {{ employee.first_name|first }}{{ employee.last_name|first }}
                {% endif %}
            </div>
            
            <!-- Employee Info -->
            <h3 class="font-semibold text-lg text-neutral-800 mb-1">{{ employee.full_name }}</h3>
            <p class="text-sm text-neutral-600 mb-2">{{ employee.employee_number }}</p>
            
            {% if employee.job_position %}
                <p class="text-sm text-primary-600 mb-2">{{ employee.job_position.title }}</p>
            {% endif %}
            
            {% if employee.department %}
                <p class="text-xs text-neutral-500 mb-4">{{ employee.department.name }}</p>
            {% endif %}
            
            <!-- Contact Info -->
            <div class="flex justify-center gap-4 mb-4 text-sm text-neutral-600">
                {% if employee.email %}
                    <div class="flex items-center">
                        <i class="fas fa-envelope ml-1"></i>
                        <span>{{ employee.email|truncatechars:20 }}</span>
                    </div>
                {% endif %}
                
                {% if employee.mobile %}
                    <div class="flex items-center">
                        <i class="fas fa-phone ml-1"></i>
                        <span>{{ employee.mobile }}</span>
                    </div>
                {% endif %}
            </div>
            
            <!-- Actions -->
            <div class="flex justify-center gap-2">
                <a href="{% url 'Hr:employee_detail' employee.id %}" 
                   class="btn btn-primary btn-sm">
                    <i class="fas fa-eye ml-1"></i>
                    عرض
                </a>
                <a href="{% url 'Hr:employee_edit' employee.id %}" 
                   class="btn btn-secondary btn-sm">
                    <i class="fas fa-edit ml-1"></i>
                    تعديل
                </a>
                <div class="dropdown">
                    <button class="btn btn-ghost btn-sm" data-dropdown-trigger>
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a href="{% url 'Hr:employee_attendance' employee.id %}" class="dropdown-item">
                            <i class="fas fa-clock ml-2"></i>
                            سجل الحضور
                        </a>
                        <a href="{% url 'Hr:employee_leaves' employee.id %}" class="dropdown-item">
                            <i class="fas fa-calendar-times ml-2"></i>
                            الإجازات
                        </a>
                        <a href="{% url 'Hr:employee_payroll' employee.id %}" class="dropdown-item">
                            <i class="fas fa-money-bill-wave ml-2"></i>
                            الرواتب
                        </a>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item text-error-600" 
                                onclick="confirmDelete('{{ employee.id }}', '{{ employee.full_name }}')">
                            <i class="fas fa-trash ml-2"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <i class="fas fa-users text-6xl text-neutral-300 mb-4"></i>
        <h3 class="text-xl font-semibold text-neutral-600 mb-2">لا توجد موظفون</h3>
        <p class="text-neutral-500 mb-4">ابدأ بإضافة موظفين جدد إلى النظام</p>
        <a href="{% url 'Hr:employee_add' %}" class="btn btn-primary">
            <i class="fas fa-plus ml-2"></i>
            إضافة أول موظف
        </a>
    </div>
    {% endfor %}
</div>

<!-- Employees Table View (Hidden by default) -->
<div id="employees-table" class="hidden">
    <div class="table-container">
        <table class="table" data-table='{"sortable": true, "searchable": true, "pagination": true}'>
            <thead>
                <tr>
                    <th data-sortable>
                        <input type="checkbox" id="select-all" class="form-checkbox">
                    </th>
                    <th data-sortable>الصورة</th>
                    <th data-sortable>الاسم</th>
                    <th data-sortable>رقم الموظف</th>
                    <th data-sortable>الوظيفة</th>
                    <th data-sortable>القسم</th>
                    <th data-sortable>الحالة</th>
                    <th data-sortable>تاريخ التوظيف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees %}
                <tr>
                    <td>
                        <input type="checkbox" class="form-checkbox employee-checkbox" 
                               value="{{ employee.id }}">
                    </td>
                    <td>
                        <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                            {% if employee.profile_picture %}
                                <img src="{{ employee.profile_picture.url }}" alt="{{ employee.full_name }}" 
                                     class="w-full h-full object-cover rounded-full">
                            {% else %}
                                <span class="text-primary-600 font-semibold">
                                    {{ employee.first_name|first }}{{ employee.last_name|first }}
                                </span>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <div>
                            <div class="font-medium">{{ employee.full_name }}</div>
                            {% if employee.email %}
                                <div class="text-sm text-neutral-500">{{ employee.email }}</div>
                            {% endif %}
                        </div>
                    </td>
                    <td>{{ employee.employee_number }}</td>
                    <td>{{ employee.job_position.title|default:"-" }}</td>
                    <td>{{ employee.department.name|default:"-" }}</td>
                    <td>
                        {% if employee.status == 'active' %}
                            <span class="badge badge-success">نشط</span>
                        {% elif employee.status == 'inactive' %}
                            <span class="badge badge-secondary">غير نشط</span>
                        {% elif employee.status == 'on_leave' %}
                            <span class="badge badge-warning">في إجازة</span>
                        {% elif employee.status == 'terminated' %}
                            <span class="badge badge-error">منتهي الخدمة</span>
                        {% endif %}
                    </td>
                    <td>{{ employee.hire_date|date:"Y/m/d" }}</td>
                    <td>
                        <div class="flex gap-1">
                            <a href="{% url 'Hr:employee_detail' employee.id %}" 
                               class="btn btn-primary btn-xs">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'Hr:employee_edit' employee.id %}" 
                               class="btn btn-secondary btn-xs">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn btn-error btn-xs" 
                                    onclick="confirmDelete('{{ employee.id }}', '{{ employee.full_name }}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="flex justify-center mt-6">
    <nav class="pagination">
        {% if page_obj.has_previous %}
            <a href="?page=1" class="pagination-link">الأولى</a>
            <a href="?page={{ page_obj.previous_page_number }}" class="pagination-link">السابقة</a>
        {% endif %}
        
        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <span class="pagination-link active">{{ num }}</span>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <a href="?page={{ num }}" class="pagination-link">{{ num }}</a>
            {% endif %}
        {% endfor %}
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="pagination-link">التالية</a>
            <a href="?page={{ page_obj.paginator.num_pages }}" class="pagination-link">الأخيرة</a>
        {% endif %}
    </nav>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal-backdrop hidden" id="delete-modal">
    <div class="modal modal-sm">
        <div class="modal-header">
            <h3 class="modal-title">تأكيد الحذف</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من حذف الموظف <strong id="employee-name"></strong>؟</p>
            <p class="text-sm text-neutral-600 mt-2">لا يمكن التراجع عن هذا الإجراء.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إلغاء</button>
            <button type="button" class="btn btn-error" id="confirm-delete">حذف</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View Toggle
    const gridView = document.getElementById('grid-view');
    const tableView = document.getElementById('table-view');
    const employeesGrid = document.getElementById('employees-grid');
    const employeesTable = document.getElementById('employees-table');
    
    gridView.addEventListener('click', function() {
        gridView.classList.add('active');
        tableView.classList.remove('active');
        employeesGrid.classList.remove('hidden');
        employeesTable.classList.add('hidden');
    });
    
    tableView.addEventListener('click', function() {
        tableView.classList.add('active');
        gridView.classList.remove('active');
        employeesTable.classList.remove('hidden');
        employeesGrid.classList.add('hidden');
    });
    
    // Search functionality
    const searchInput = document.getElementById('employee-search');
    const departmentFilter = document.getElementById('department-filter');
    const statusFilter = document.getElementById('status-filter');
    const employmentTypeFilter = document.getElementById('employment-type-filter');
    
    function filterEmployees() {
        const searchTerm = searchInput.value.toLowerCase();
        const department = departmentFilter.value;
        const status = statusFilter.value;
        const employmentType = employmentTypeFilter.value;
        
        const employeeCards = document.querySelectorAll('.employee-card');
        
        employeeCards.forEach(card => {
            const employeeName = card.querySelector('h3').textContent.toLowerCase();
            const employeeNumber = card.querySelector('p').textContent.toLowerCase();
            const employeeDepartment = card.dataset.department || '';
            const employeeStatus = card.dataset.status || '';
            const employeeType = card.dataset.employmentType || '';
            
            const matchesSearch = employeeName.includes(searchTerm) || employeeNumber.includes(searchTerm);
            const matchesDepartment = !department || employeeDepartment === department;
            const matchesStatus = !status || employeeStatus === status;
            const matchesType = !employmentType || employeeType === employmentType;
            
            if (matchesSearch && matchesDepartment && matchesStatus && matchesType) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    searchInput.addEventListener('input', HRComponents.utils.debounce(filterEmployees, 300));
    departmentFilter.addEventListener('change', filterEmployees);
    statusFilter.addEventListener('change', filterEmployees);
    employmentTypeFilter.addEventListener('change', filterEmployees);
    
    // Select All functionality
    const selectAll = document.getElementById('select-all');
    const employeeCheckboxes = document.querySelectorAll('.employee-checkbox');
    
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            employeeCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        });
    }
    
    // Export functionality
    document.getElementById('export-excel').addEventListener('click', function() {
        const selectedEmployees = getSelectedEmployees();
        exportEmployees('excel', selectedEmployees);
    });
    
    document.getElementById('export-pdf').addEventListener('click', function() {
        const selectedEmployees = getSelectedEmployees();
        exportEmployees('pdf', selectedEmployees);
    });
    
    function getSelectedEmployees() {
        const selected = [];
        employeeCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selected.push(checkbox.value);
            }
        });
        return selected;
    }
    
    function exportEmployees(format, employeeIds) {
        showLoading();
        
        const params = new URLSearchParams({
            format: format,
            employee_ids: employeeIds.join(',')
        });
        
        fetch(`{% url 'Hr:employee_export' %}?${params}`)
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Export failed');
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `employees.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                HRComponents.Toast.show('تم تصدير البيانات بنجاح', 'success');
            })
            .catch(error => {
                console.error('Export error:', error);
                HRComponents.Toast.show('فشل في تصدير البيانات', 'error');
            })
            .finally(() => {
                hideLoading();
            });
    }
});

// Delete confirmation
let employeeToDelete = null;

function confirmDelete(employeeId, employeeName) {
    employeeToDelete = employeeId;
    document.getElementById('employee-name').textContent = employeeName;
    
    const modal = document.getElementById('delete-modal');
    modal.classList.remove('hidden');
}

document.getElementById('confirm-delete').addEventListener('click', function() {
    if (employeeToDelete) {
        deleteEmployee(employeeToDelete);
    }
});

function deleteEmployee(employeeId) {
    showLoading();
    
    fetch(`{% url 'Hr:employee_delete' 'PLACEHOLDER' %}`.replace('PLACEHOLDER', employeeId), {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            // Remove employee card from DOM
            const employeeCard = document.querySelector(`[data-employee-id="${employeeId}"]`);
            if (employeeCard) {
                employeeCard.remove();
            }
            
            HRComponents.Toast.show('تم حذف الموظف بنجاح', 'success');
            
            // Hide modal
            document.getElementById('delete-modal').classList.add('hidden');
        } else {
            throw new Error('Delete failed');
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        HRComponents.Toast.show('فشل في حذف الموظف', 'error');
    })
    .finally(() => {
        hideLoading();
        employeeToDelete = null;
    });
}
</script>
{% endblock %}