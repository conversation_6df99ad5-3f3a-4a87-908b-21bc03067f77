{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}إدارة الورديات{% endblock %}
{% block page_title %}إدارة الورديات{% endblock %}

{% block breadcrumb_items %}
<div class="breadcrumb-item">
    <a href="{% url 'Hr:attendance_dashboard' %}" class="breadcrumb-link">الحضور والانصراف</a>
</div>
<div class="breadcrumb-item">
    <span class="breadcrumb-link">إدارة الورديات</span>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .shift-card {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-sm);
        transition: all var(--transition-fast);
        border-left: 4px solid var(--primary-500);
    }
    
    .shift-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .shift-card.inactive {
        border-left-color: var(--neutral-300);
        opacity: 0.7;
    }
    
    .shift-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--space-4);
    }
    
    .shift-name {
        font-size: var(--text-xl);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        margin-bottom: var(--space-1);
    }
    
    .shift-description {
        color: var(--neutral-600);
        font-size: var(--text-sm);
    }
    
    .shift-status {
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
    }
    
    .shift-status.active {
        background-color: var(--success-100);
        color: var(--success-700);
    }
    
    .shift-status.inactive {
        background-color: var(--neutral-200);
        color: var(--neutral-700);
    }
    
    .shift-times {
        display: flex;
        gap: var(--space-4);
        margin-bottom: var(--space-4);
    }
    
    .shift-time {
        flex: 1;
        text-align: center;
        padding: var(--space-3);
        background-color: var(--neutral-50);
        border-radius: var(--radius-md);
    }
    
    .shift-time-label {
        font-size: var(--text-xs);
        color: var(--neutral-600);
        margin-bottom: var(--space-1);
    }
    
    .shift-time-value {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        direction: ltr;
    }
    
    .shift-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--space-3);
        margin-bottom: var(--space-4);
    }
    
    .shift-detail {
        text-align: center;
    }
    
    .shift-detail-label {
        font-size: var(--text-xs);
        color: var(--neutral-600);
        margin-bottom: var(--space-1);
    }
    
    .shift-detail-value {
        font-weight: var(--font-medium);
        color: var(--neutral-800);
    }
    
    .shift-employees {
        margin-bottom: var(--space-4);
    }
    
    .shift-employees-count {
        font-size: var(--text-sm);
        color: var(--neutral-600);
        margin-bottom: var(--space-2);
    }
    
    .employee-avatars {
        display: flex;
        gap: var(--space-1);
        flex-wrap: wrap;
    }
    
    .employee-avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background-color: var(--primary-100);
        color: var(--primary-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-xs);
        font-weight: var(--font-semibold);
        border: 2px solid white;
        box-shadow: var(--shadow-sm);
    }
    
    .shift-actions {
        display: flex;
        gap: var(--space-2);
        justify-content: flex-end;
    }
    
    .filters-section {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        box-shadow: var(--shadow-sm);
    }
    
    .time-input-group {
        display: flex;
        align-items: center;
        gap: var(--space-2);
    }
    
    .time-input-group input {
        direction: ltr;
        text-align: center;
    }
    
    @media (max-width: 768px) {
        .shift-times {
            flex-direction: column;
            gap: var(--space-2);
        }
        
        .shift-details {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .shift-actions {
            justify-content: stretch;
        }
        
        .shift-actions .btn {
            flex: 1;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Filters and Actions -->
<div class="filters-section">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <h2 class="text-xl font-semibold text-neutral-800">إدارة الورديات</h2>
        
        <div class="flex items-center gap-3">
            <button class="btn btn-outline btn-sm" id="import-shifts">
                <i class="fas fa-file-import ml-2"></i>
                استيراد ورديات
            </button>
            <button class="btn btn-primary" onclick="showAddShiftModal()">
                <i class="fas fa-plus ml-2"></i>
                إضافة وردية جديدة
            </button>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="form-group">
            <input type="text" id="search-shifts" class="form-input" 
                   placeholder="البحث في الورديات...">
        </div>
        
        <div class="form-group">
            <select id="status-filter" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="active">نشطة</option>
                <option value="inactive">غير نشطة</option>
            </select>
        </div>
        
        <div class="form-group">
            <select id="department-filter" class="form-select">
                <option value="">جميع الأقسام</option>
                {% for department in departments %}
                    <option value="{{ department.id }}">{{ department.name }}</option>
                {% endfor %}
            </select>
        </div>
    </div>
</div>

<!-- Work Shifts Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="shifts-grid">
    {% for shift in shifts %}
    <div class="shift-card {% if not shift.is_active %}inactive{% endif %}" data-shift-id="{{ shift.id }}">
        <div class="shift-header">
            <div>
                <div class="shift-name">{{ shift.name }}</div>
                <div class="shift-description">{{ shift.description|default:"لا يوجد وصف" }}</div>
            </div>
            <div class="shift-status {% if shift.is_active %}active{% else %}inactive{% endif %}">
                {% if shift.is_active %}نشطة{% else %}غير نشطة{% endif %}
            </div>
        </div>
        
        <div class="shift-times">
            <div class="shift-time">
                <div class="shift-time-label">بداية الوردية</div>
                <div class="shift-time-value">{{ shift.start_time|time:"H:i" }}</div>
            </div>
            <div class="shift-time">
                <div class="shift-time-label">نهاية الوردية</div>
                <div class="shift-time-value">{{ shift.end_time|time:"H:i" }}</div>
            </div>
        </div>
        
        <div class="shift-details">
            <div class="shift-detail">
                <div class="shift-detail-label">مدة الوردية</div>
                <div class="shift-detail-value">{{ shift.duration_hours }} ساعة</div>
            </div>
            <div class="shift-detail">
                <div class="shift-detail-label">فترة السماح</div>
                <div class="shift-detail-value">{{ shift.grace_period_minutes }} دقيقة</div>
            </div>
            <div class="shift-detail">
                <div class="shift-detail-label">استراحة</div>
                <div class="shift-detail-value">{{ shift.break_duration_minutes }} دقيقة</div>
            </div>
            <div class="shift-detail">
                <div class="shift-detail-label">أيام العمل</div>
                <div class="shift-detail-value">{{ shift.working_days_count }} أيام</div>
            </div>
        </div>
        
        <div class="shift-employees">
            <div class="shift-employees-count">
                {{ shift.employees.count }} موظف في هذه الوردية
            </div>
            <div class="employee-avatars">
                {% for employee in shift.employees.all|slice:":10" %}
                    <div class="employee-avatar" title="{{ employee.full_name }}">
                        {% if employee.profile_picture %}
                            <img src="{{ employee.profile_picture.url }}" alt="{{ employee.full_name }}" 
                                 class="w-full h-full object-cover rounded-full">
                        {% else %}
                            {{ employee.first_name|first }}{{ employee.last_name|first }}
                        {% endif %}
                    </div>
                {% endfor %}
                {% if shift.employees.count > 10 %}
                    <div class="employee-avatar">+{{ shift.employees.count|add:"-10" }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="shift-actions">
            <button class="btn btn-secondary btn-sm" onclick="assignEmployees('{{ shift.id }}')">
                <i class="fas fa-users"></i>
            </button>
            <button class="btn btn-primary btn-sm" onclick="editShift('{{ shift.id }}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-error btn-sm" onclick="deleteShift('{{ shift.id }}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <i class="fas fa-clock text-6xl text-neutral-300 mb-4 block"></i>
        <h3 class="text-lg font-semibold text-neutral-600 mb-2">لا توجد ورديات</h3>
        <p class="text-neutral-500 mb-4">ابدأ بإضافة ورديات العمل لتنظيم أوقات الحضور</p>
        <button class="btn btn-primary" onclick="showAddShiftModal()">
            <i class="fas fa-plus ml-2"></i>
            إضافة وردية جديدة
        </button>
    </div>
    {% endfor %}
</div><!-- Add/Ed
it Shift Modal -->
<div class="modal-backdrop hidden" id="shift-modal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <h3 class="modal-title" id="shift-modal-title">إضافة وردية جديدة</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body">
            <form id="shift-form">
                <input type="hidden" id="shift-id" name="shift_id">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="shift-name" class="form-label required">اسم الوردية</label>
                        <input type="text" id="shift-name" name="name" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="shift-code" class="form-label">رمز الوردية</label>
                        <input type="text" id="shift-code" name="code" class="form-input">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="shift-description" class="form-label">الوصف</label>
                    <textarea id="shift-description" name="description" class="form-textarea" rows="2"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="shift-start-time" class="form-label required">وقت البداية</label>
                        <div class="time-input-group">
                            <input type="time" id="shift-start-time" name="start_time" class="form-input" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="shift-end-time" class="form-label required">وقت النهاية</label>
                        <div class="time-input-group">
                            <input type="time" id="shift-end-time" name="end_time" class="form-input" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="grace-period" class="form-label">فترة السماح (دقيقة)</label>
                        <input type="number" id="grace-period" name="grace_period_minutes" 
                               class="form-input" min="0" max="60" value="15">
                    </div>
                    
                    <div class="form-group">
                        <label for="break-duration" class="form-label">مدة الاستراحة (دقيقة)</label>
                        <input type="number" id="break-duration" name="break_duration_minutes" 
                               class="form-input" min="0" max="120" value="60">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">أيام العمل</label>
                    <div class="grid grid-cols-4 gap-2 mt-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="0" class="form-checkbox">
                            <span class="mr-2">الأحد</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="1" class="form-checkbox">
                            <span class="mr-2">الاثنين</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="2" class="form-checkbox">
                            <span class="mr-2">الثلاثاء</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="3" class="form-checkbox">
                            <span class="mr-2">الأربعاء</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="4" class="form-checkbox">
                            <span class="mr-2">الخميس</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="5" class="form-checkbox">
                            <span class="mr-2">الجمعة</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="working_days" value="6" class="form-checkbox">
                            <span class="mr-2">السبت</span>
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="flex items-center">
                        <input type="checkbox" id="shift-is-active" name="is_active" class="form-checkbox" checked>
                        <label for="shift-is-active" class="mr-2">وردية نشطة</label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إلغاء</button>
            <button type="button" class="btn btn-primary" id="save-shift">حفظ</button>
        </div>
    </div>
</div>

<!-- Assign Employees Modal -->
<div class="modal-backdrop hidden" id="assign-employees-modal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <h3 class="modal-title">تعيين الموظفين للوردية</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body">
            <input type="hidden" id="assign-shift-id">
            
            <div class="form-group mb-4">
                <input type="text" id="employee-search" class="form-input" 
                       placeholder="البحث عن موظف...">
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Available Employees -->
                <div>
                    <h4 class="font-semibold text-neutral-800 mb-3">الموظفون المتاحون</h4>
                    <div class="border border-neutral-200 rounded-lg p-4 max-h-80 overflow-y-auto" id="available-employees">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
                
                <!-- Assigned Employees -->
                <div>
                    <h4 class="font-semibold text-neutral-800 mb-3">الموظفون المعينون</h4>
                    <div class="border border-neutral-200 rounded-lg p-4 max-h-80 overflow-y-auto" id="assigned-employees">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إلغاء</button>
            <button type="button" class="btn btn-primary" id="save-assignments">حفظ التعيينات</button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal-backdrop hidden" id="delete-shift-modal">
    <div class="modal modal-sm">
        <div class="modal-header">
            <h3 class="modal-title">تأكيد الحذف</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من حذف هذه الوردية؟</p>
            <p class="text-sm text-neutral-600 mt-2">سيتم إلغاء تعيين جميع الموظفين من هذه الوردية.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إلغاء</button>
            <button type="button" class="btn btn-error" id="confirm-delete-shift">حذف</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('search-shifts');
    const statusFilter = document.getElementById('status-filter');
    const departmentFilter = document.getElementById('department-filter');
    
    function filterShifts() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const departmentValue = departmentFilter.value;
        
        const shiftCards = document.querySelectorAll('.shift-card');
        
        shiftCards.forEach(card => {
            const shiftName = card.querySelector('.shift-name').textContent.toLowerCase();
            const shiftDescription = card.querySelector('.shift-description').textContent.toLowerCase();
            const isActive = !card.classList.contains('inactive');
            
            let showCard = true;
            
            // Filter by search term
            if (searchTerm && !shiftName.includes(searchTerm) && !shiftDescription.includes(searchTerm)) {
                showCard = false;
            }
            
            // Filter by status
            if (statusValue === 'active' && !isActive) {
                showCard = false;
            } else if (statusValue === 'inactive' && isActive) {
                showCard = false;
            }
            
            card.style.display = showCard ? 'block' : 'none';
        });
    }
    
    searchInput.addEventListener('input', HRComponents.utils.debounce(filterShifts, 300));
    statusFilter.addEventListener('change', filterShifts);
    departmentFilter.addEventListener('change', filterShifts);
    
    // Modal functions
    window.showAddShiftModal = function() {
        document.getElementById('shift-modal-title').textContent = 'إضافة وردية جديدة';
        document.getElementById('shift-form').reset();
        document.getElementById('shift-id').value = '';
        document.getElementById('shift-is-active').checked = true;
        
        // Set default working days (Sunday to Thursday)
        const workingDaysCheckboxes = document.querySelectorAll('input[name="working_days"]');
        workingDaysCheckboxes.forEach((checkbox, index) => {
            checkbox.checked = index <= 4; // Sunday (0) to Thursday (4)
        });
        
        document.getElementById('shift-modal').classList.remove('hidden');
    };
    
    window.editShift = function(shiftId) {
        showLoading();
        
        fetch(`{% url 'Hr:api_get_work_shift' 0 %}`.replace('0', shiftId))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const shift = data.shift;
                    
                    document.getElementById('shift-modal-title').textContent = 'تعديل الوردية';
                    document.getElementById('shift-id').value = shift.id;
                    document.getElementById('shift-name').value = shift.name;
                    document.getElementById('shift-code').value = shift.code || '';
                    document.getElementById('shift-description').value = shift.description || '';
                    document.getElementById('shift-start-time').value = shift.start_time;
                    document.getElementById('shift-end-time').value = shift.end_time;
                    document.getElementById('grace-period').value = shift.grace_period_minutes;
                    document.getElementById('break-duration').value = shift.break_duration_minutes;
                    document.getElementById('shift-is-active').checked = shift.is_active;
                    
                    // Set working days
                    const workingDaysCheckboxes = document.querySelectorAll('input[name="working_days"]');
                    workingDaysCheckboxes.forEach(checkbox => {
                        checkbox.checked = shift.working_days.includes(parseInt(checkbox.value));
                    });
                    
                    document.getElementById('shift-modal').classList.remove('hidden');
                } else {
                    throw new Error(data.message || 'فشل في تحميل بيانات الوردية');
                }
            })
            .catch(error => {
                console.error('Error loading shift:', error);
                HRComponents.Toast.show(error.message || 'فشل في تحميل بيانات الوردية', 'error');
            })
            .finally(() => {
                hideLoading();
            });
    };
    
    window.assignEmployees = function(shiftId) {
        document.getElementById('assign-shift-id').value = shiftId;
        loadEmployeesForAssignment(shiftId);
        document.getElementById('assign-employees-modal').classList.remove('hidden');
    };
    
    let shiftToDelete = null;
    window.deleteShift = function(shiftId) {
        shiftToDelete = shiftId;
        document.getElementById('delete-shift-modal').classList.remove('hidden');
    };
    
    // Save shift
    document.getElementById('save-shift').addEventListener('click', function() {
        const form = document.getElementById('shift-form');
        const formData = new FormData(form);
        
        // Validate required fields
        const name = formData.get('name');
        const startTime = formData.get('start_time');
        const endTime = formData.get('end_time');
        
        if (!name || !startTime || !endTime) {
            HRComponents.Toast.show('يرجى ملء الحقول المطلوبة', 'error');
            return;
        }
        
        // Collect working days
        const workingDays = Array.from(document.querySelectorAll('input[name="working_days"]:checked'))
            .map(cb => cb.value);
        
        if (workingDays.length === 0) {
            HRComponents.Toast.show('يرجى اختيار يوم واحد على الأقل للعمل', 'error');
            return;
        }
        
        // Add working days to form data
        formData.delete('working_days');
        workingDays.forEach(day => {
            formData.append('working_days', day);
        });
        
        showLoading();
        
        const shiftId = formData.get('shift_id');
        const url = shiftId ? 
            `{% url 'Hr:api_update_work_shift' 0 %}`.replace('0', shiftId) :
            '{% url "Hr:api_create_work_shift" %}';
        
        fetch(url, {
            method: shiftId ? 'PUT' : 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show(shiftId ? 'تم تحديث الوردية بنجاح' : 'تم إضافة الوردية بنجاح', 'success');
                document.getElementById('shift-modal').classList.add('hidden');
                
                // Reload page after short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                throw new Error(data.message || 'فشل في حفظ الوردية');
            }
        })
        .catch(error => {
            console.error('Error saving shift:', error);
            HRComponents.Toast.show(error.message || 'فشل في حفظ الوردية', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    // Confirm delete shift
    document.getElementById('confirm-delete-shift').addEventListener('click', function() {
        if (!shiftToDelete) return;
        
        showLoading();
        
        fetch(`{% url 'Hr:api_delete_work_shift' 0 %}`.replace('0', shiftToDelete), {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show('تم حذف الوردية بنجاح', 'success');
                document.getElementById('delete-shift-modal').classList.add('hidden');
                
                // Remove card from grid
                const card = document.querySelector(`[data-shift-id="${shiftToDelete}"]`);
                if (card) {
                    card.remove();
                }
                
                shiftToDelete = null;
            } else {
                throw new Error(data.message || 'فشل في حذف الوردية');
            }
        })
        .catch(error => {
            console.error('Error deleting shift:', error);
            HRComponents.Toast.show(error.message || 'فشل في حذف الوردية', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    // Load employees for assignment
    function loadEmployeesForAssignment(shiftId) {
        showLoading();
        
        fetch(`{% url 'Hr:api_get_shift_employees' 0 %}`.replace('0', shiftId))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderEmployeeLists(data.available_employees, data.assigned_employees);
                } else {
                    throw new Error(data.message || 'فشل في تحميل قائمة الموظفين');
                }
            })
            .catch(error => {
                console.error('Error loading employees:', error);
                HRComponents.Toast.show(error.message || 'فشل في تحميل قائمة الموظفين', 'error');
            })
            .finally(() => {
                hideLoading();
            });
    }
    
    function renderEmployeeLists(availableEmployees, assignedEmployees) {
        const availableContainer = document.getElementById('available-employees');
        const assignedContainer = document.getElementById('assigned-employees');
        
        // Render available employees
        availableContainer.innerHTML = availableEmployees.map(employee => `
            <div class="flex items-center justify-between p-2 hover:bg-neutral-50 rounded" data-employee-id="${employee.id}">
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                        <span class="text-primary-600 text-sm font-semibold">
                            ${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}
                        </span>
                    </div>
                    <div>
                        <div class="font-medium">${employee.full_name}</div>
                        <div class="text-xs text-neutral-500">${employee.department || 'بدون قسم'}</div>
                    </div>
                </div>
                <button class="btn btn-primary btn-xs" onclick="assignEmployee(${employee.id})">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        `).join('');
        
        // Render assigned employees
        assignedContainer.innerHTML = assignedEmployees.map(employee => `
            <div class="flex items-center justify-between p-2 hover:bg-neutral-50 rounded" data-employee-id="${employee.id}">
                <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-full bg-success-100 flex items-center justify-center">
                        <span class="text-success-600 text-sm font-semibold">
                            ${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}
                        </span>
                    </div>
                    <div>
                        <div class="font-medium">${employee.full_name}</div>
                        <div class="text-xs text-neutral-500">${employee.department || 'بدون قسم'}</div>
                    </div>
                </div>
                <button class="btn btn-error btn-xs" onclick="unassignEmployee(${employee.id})">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        `).join('');
    }
    
    // Employee assignment functions
    window.assignEmployee = function(employeeId) {
        const employeeElement = document.querySelector(`#available-employees [data-employee-id="${employeeId}"]`);
        if (employeeElement) {
            const assignedContainer = document.getElementById('assigned-employees');
            
            // Update button
            const button = employeeElement.querySelector('button');
            button.className = 'btn btn-error btn-xs';
            button.innerHTML = '<i class="fas fa-minus"></i>';
            button.setAttribute('onclick', `unassignEmployee(${employeeId})`);
            
            // Update avatar color
            const avatar = employeeElement.querySelector('.bg-primary-100');
            avatar.className = avatar.className.replace('bg-primary-100', 'bg-success-100');
            avatar.querySelector('span').className = avatar.querySelector('span').className.replace('text-primary-600', 'text-success-600');
            
            // Move to assigned container
            assignedContainer.appendChild(employeeElement);
        }
    };
    
    window.unassignEmployee = function(employeeId) {
        const employeeElement = document.querySelector(`#assigned-employees [data-employee-id="${employeeId}"]`);
        if (employeeElement) {
            const availableContainer = document.getElementById('available-employees');
            
            // Update button
            const button = employeeElement.querySelector('button');
            button.className = 'btn btn-primary btn-xs';
            button.innerHTML = '<i class="fas fa-plus"></i>';
            button.setAttribute('onclick', `assignEmployee(${employeeId})`);
            
            // Update avatar color
            const avatar = employeeElement.querySelector('.bg-success-100');
            avatar.className = avatar.className.replace('bg-success-100', 'bg-primary-100');
            avatar.querySelector('span').className = avatar.querySelector('span').className.replace('text-success-600', 'text-primary-600');
            
            // Move to available container
            availableContainer.appendChild(employeeElement);
        }
    };
    
    // Save assignments
    document.getElementById('save-assignments').addEventListener('click', function() {
        const shiftId = document.getElementById('assign-shift-id').value;
        const assignedEmployeeIds = Array.from(document.querySelectorAll('#assigned-employees [data-employee-id]'))
            .map(el => el.getAttribute('data-employee-id'));
        
        showLoading();
        
        fetch('{% url "Hr:api_assign_shift_employees" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                shift_id: shiftId,
                employee_ids: assignedEmployeeIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show('تم حفظ تعيينات الموظفين بنجاح', 'success');
                document.getElementById('assign-employees-modal').classList.add('hidden');
                
                // Reload page after short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                throw new Error(data.message || 'فشل في حفظ التعيينات');
            }
        })
        .catch(error => {
            console.error('Error saving assignments:', error);
            HRComponents.Toast.show(error.message || 'فشل في حفظ التعيينات', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    // Employee search in assignment modal
    document.getElementById('employee-search').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const employeeElements = document.querySelectorAll('#available-employees [data-employee-id], #assigned-employees [data-employee-id]');
        
        employeeElements.forEach(element => {
            const employeeName = element.querySelector('.font-medium').textContent.toLowerCase();
            const employeeDept = element.querySelector('.text-neutral-500').textContent.toLowerCase();
            
            if (employeeName.includes(searchTerm) || employeeDept.includes(searchTerm)) {
                element.style.display = 'flex';
            } else {
                element.style.display = 'none';
            }
        });
    });
});
</script>
{% endblock %}