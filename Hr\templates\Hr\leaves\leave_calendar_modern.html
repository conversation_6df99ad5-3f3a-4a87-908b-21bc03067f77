{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تقويم الإجازات{% endblock %}
{% block page_title %}تقويم الإجازات{% endblock %}

{% block breadcrumb_items %}
<div class="breadcrumb-item">
    <a href="{% url 'Hr:leaves_dashboard' %}" class="breadcrumb-link">الإجازات</a>
</div>
<div class="breadcrumb-item">
    <span class="breadcrumb-link">تقويم الإجازات</span>
</div>
{% endblock %}

{% block extra_css %}
<!-- FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">

<style>
    .calendar-container {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--space-6);
    }
    
    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-6);
    }
    
    .calendar-filters {
        display: flex;
        gap: var(--space-3);
        align-items: center;
    }
    
    .calendar-legend {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-size: var(--text-sm);
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: var(--radius-sm);
    }
    
    .stats-sidebar {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-sm);
    }
    
    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-3) 0;
        border-bottom: 1px solid var(--neutral-100);
    }
    
    .stat-item:last-child {
        border-bottom: none;
    }
    
    .stat-label {
        color: var(--neutral-600);
        font-size: var(--text-sm);
    }
    
    .stat-value {
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
    }
    
    .upcoming-leaves {
        margin-top: var(--space-6);
    }
    
    .upcoming-leave-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-3);
        border-radius: var(--radius-md);
        margin-bottom: var(--space-2);
        transition: all var(--transition-fast);
    }
    
    .upcoming-leave-item:hover {
        background-color: var(--neutral-50);
    }
    
    .leave-date {
        font-size: var(--text-xs);
        color: var(--neutral-500);
        min-width: 60px;
    }
    
    .leave-info {
        flex: 1;
    }
    
    .leave-employee {
        font-weight: var(--font-medium);
        color: var(--neutral-800);
        margin-bottom: var(--space-1);
    }
    
    .leave-type {
        font-size: var(--text-xs);
        color: var(--neutral-600);
    }
    
    .leave-status {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
    }
    
    /* FullCalendar Customizations */
    .fc {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: rtl;
    }
    
    .fc-toolbar {
        margin-bottom: var(--space-4) !important;
    }
    
    .fc-toolbar-title {
        font-size: var(--text-xl) !important;
        font-weight: var(--font-semibold) !important;
        color: var(--neutral-800) !important;
    }
    
    .fc-button {
        background-color: var(--primary-600) !important;
        border-color: var(--primary-600) !important;
        color: white !important;
        border-radius: var(--radius-md) !important;
        padding: var(--space-2) var(--space-3) !important;
        font-size: var(--text-sm) !important;
    }
    
    .fc-button:hover {
        background-color: var(--primary-700) !important;
        border-color: var(--primary-700) !important;
    }
    
    .fc-button:disabled {
        background-color: var(--neutral-300) !important;
        border-color: var(--neutral-300) !important;
        color: var(--neutral-500) !important;
    }
    
    .fc-daygrid-day {
        border-color: var(--neutral-200) !important;
    }
    
    .fc-daygrid-day-number {
        color: var(--neutral-700) !important;
        font-weight: var(--font-medium) !important;
    }
    
    .fc-day-today {
        background-color: var(--primary-50) !important;
    }
    
    .fc-day-today .fc-daygrid-day-number {
        color: var(--primary-700) !important;
        font-weight: var(--font-bold) !important;
    }
    
    .fc-event {
        border-radius: var(--radius-sm) !important;
        border: none !important;
        font-size: var(--text-xs) !important;
        padding: 2px 4px !important;
        margin: 1px 0 !important;
    }
    
    .fc-event-title {
        font-weight: var(--font-medium) !important;
    }
    
    /* Leave type colors */
    .leave-annual {
        background-color: var(--success-500) !important;
    }
    
    .leave-sick {
        background-color: var(--error-500) !important;
    }
    
    .leave-emergency {
        background-color: var(--warning-500) !important;
    }
    
    .leave-maternity {
        background-color: var(--primary-500) !important;
    }
    
    .leave-unpaid {
        background-color: var(--neutral-500) !important;
    }
    
    .leave-pending {
        opacity: 0.7;
        border: 2px dashed var(--neutral-400) !important;
    }
    
    @media (max-width: 768px) {
        .calendar-header {
            flex-direction: column;
            gap: var(--space-4);
            align-items: stretch;
        }
        
        .calendar-filters {
            justify-content: center;
        }
        
        .calendar-legend {
            justify-content: center;
        }
        
        .fc-toolbar {
            flex-direction: column !important;
            gap: var(--space-2) !important;
        }
        
        .fc-toolbar-chunk {
            display: flex !important;
            justify-content: center !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- Calendar Section -->
    <div class="lg:col-span-3">
        <div class="calendar-container">
            <div class="calendar-header">
                <h2 class="text-xl font-semibold text-neutral-800">تقويم الإجازات</h2>
                
                <div class="calendar-filters">
                    <select id="department-filter" class="form-select form-select-sm">
                        <option value="">جميع الأقسام</option>
                        {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                        {% endfor %}
                    </select>
                    
                    <select id="leave-type-filter" class="form-select form-select-sm">
                        <option value="">جميع أنواع الإجازات</option>
                        {% for leave_type in leave_types %}
                            <option value="{{ leave_type.id }}">{{ leave_type.name }}</option>
                        {% endfor %}
                    </select>
                    
                    <select id="status-filter" class="form-select form-select-sm">
                        <option value="">جميع الحالات</option>
                        <option value="approved">موافق عليها</option>
                        <option value="pending">معلقة</option>
                        <option value="rejected">مرفوضة</option>
                    </select>
                </div>
            </div>
            
            <!-- Calendar Legend -->
            <div class="calendar-legend">
                <div class="legend-item">
                    <div class="legend-color leave-annual"></div>
                    <span>إجازة سنوية</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color leave-sick"></div>
                    <span>إجازة مرضية</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color leave-emergency"></div>
                    <span>إجازة طارئة</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color leave-maternity"></div>
                    <span>إجازة أمومة</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color leave-unpaid"></div>
                    <span>إجازة بدون راتب</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color bg-neutral-300" style="border: 2px dashed var(--neutral-400);"></div>
                    <span>معلقة</span>
                </div>
            </div>
            
            <!-- Calendar -->
            <div id="calendar"></div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1">
        <!-- Statistics -->
        <div class="stats-sidebar">
            <h3 class="text-lg font-semibold text-neutral-800 mb-4">إحصائيات الشهر</h3>
            
            <div class="stat-item">
                <span class="stat-label">إجمالي الإجازات</span>
                <span class="stat-value" id="total-leaves">{{ monthly_stats.total_leaves }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">موافق عليها</span>
                <span class="stat-value text-success-600" id="approved-leaves">{{ monthly_stats.approved_leaves }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">معلقة</span>
                <span class="stat-value text-warning-600" id="pending-leaves">{{ monthly_stats.pending_leaves }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">مرفوضة</span>
                <span class="stat-value text-error-600" id="rejected-leaves">{{ monthly_stats.rejected_leaves }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">إجمالي الأيام</span>
                <span class="stat-value" id="total-days">{{ monthly_stats.total_days }}</span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">متوسط المدة</span>
                <span class="stat-value" id="avg-duration">{{ monthly_stats.avg_duration|floatformat:1 }} يوم</span>
            </div>
        </div>
        
        <!-- Upcoming Leaves -->
        <div class="upcoming-leaves">
            <h3 class="text-lg font-semibold text-neutral-800 mb-4">الإجازات القادمة</h3>
            
            <div id="upcoming-leaves-list">
                {% for leave in upcoming_leaves %}
                <div class="upcoming-leave-item">
                    <div class="leave-date">
                        {{ leave.start_date|date:"m/d" }}
                    </div>
                    <div class="leave-info">
                        <div class="leave-employee">{{ leave.employee.full_name }}</div>
                        <div class="leave-type">{{ leave.leave_type.name }}</div>
                    </div>
                    <div class="leave-status status-{{ leave.status }}">
                        {% if leave.status == 'pending' %}
                            معلقة
                        {% elif leave.status == 'approved' %}
                            موافق
                        {% elif leave.status == 'rejected' %}
                            مرفوضة
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-neutral-500 py-4">
                    <i class="fas fa-calendar-check text-2xl mb-2 block"></i>
                    <p>لا توجد إجازات قادمة</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-neutral-800 mb-4">إجراءات سريعة</h3>
            
            <div class="space-y-2">
                <button class="btn btn-primary w-full" onclick="showNewLeaveModal()">
                    <i class="fas fa-plus ml-2"></i>
                    طلب إجازة جديد
                </button>
                
                <button class="btn btn-outline w-full" onclick="exportCalendar()">
                    <i class="fas fa-file-export ml-2"></i>
                    تصدير التقويم
                </button>
                
                <button class="btn btn-outline w-full" onclick="printCalendar()">
                    <i class="fas fa-print ml-2"></i>
                    طباعة التقويم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Leave Details Modal -->
<div class="modal-backdrop hidden" id="leave-details-modal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <h3 class="modal-title" id="leave-details-title">تفاصيل الإجازة</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body" id="leave-details-content">
            <!-- Content will be populated by JavaScript -->
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إغلاق</button>
            <div id="leave-actions">
                <!-- Action buttons will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let calendar;
    
    // Initialize calendar
    initializeCalendar();
    
    // Filter event listeners
    document.getElementById('department-filter').addEventListener('change', refreshCalendar);
    document.getElementById('leave-type-filter').addEventListener('change', refreshCalendar);
    document.getElementById('status-filter').addEventListener('change', refreshCalendar);
    
    function initializeCalendar() {
        const calendarEl = document.getElementById('calendar');
        
        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,dayGridWeek,listWeek'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                list: 'قائمة'
            },
            dayHeaderFormat: { weekday: 'short' },
            height: 'auto',
            events: function(fetchInfo, successCallback, failureCallback) {
                loadCalendarEvents(fetchInfo, successCallback, failureCallback);
            },
            eventClick: function(info) {
                showLeaveDetails(info.event.id);
            },
            eventDidMount: function(info) {
                // Add custom styling based on leave type and status
                const leaveType = info.event.extendedProps.leave_type_slug;
                const status = info.event.extendedProps.status;
                
                info.el.classList.add(`leave-${leaveType}`);
                if (status === 'pending') {
                    info.el.classList.add('leave-pending');
                }
                
                // Add tooltip
                info.el.title = `${info.event.extendedProps.employee_name} - ${info.event.title}`;
            },
            datesSet: function(dateInfo) {
                // Update statistics when month changes
                updateMonthlyStats(dateInfo.start, dateInfo.end);
            }
        });
        
        calendar.render();
    }
    
    function loadCalendarEvents(fetchInfo, successCallback, failureCallback) {
        const params = new URLSearchParams({
            start: fetchInfo.startStr,
            end: fetchInfo.endStr,
            department: document.getElementById('department-filter').value,
            leave_type: document.getElementById('leave-type-filter').value,
            status: document.getElementById('status-filter').value
        });
        
        fetch(`{% url 'Hr:api_get_calendar_events' %}?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const events = data.events.map(event => ({
                        id: event.id,
                        title: event.title,
                        start: event.start,
                        end: event.end,
                        allDay: true,
                        extendedProps: {
                            employee_name: event.employee_name,
                            leave_type_slug: event.leave_type_slug,
                            status: event.status,
                            days: event.days
                        }
                    }));
                    successCallback(events);
                } else {
                    failureCallback(data.message || 'فشل في تحميل أحداث التقويم');
                }
            })
            .catch(error => {
                console.error('Error loading calendar events:', error);
                failureCallback(error.message);
            });
    }
    
    function refreshCalendar() {
        calendar.refetchEvents();
    }
    
    function updateMonthlyStats(startDate, endDate) {
        const params = new URLSearchParams({
            start: startDate.toISOString().split('T')[0],
            end: endDate.toISOString().split('T')[0],
            department: document.getElementById('department-filter').value,
            leave_type: document.getElementById('leave-type-filter').value,
            status: document.getElementById('status-filter').value
        });
        
        fetch(`{% url 'Hr:api_get_monthly_leave_stats' %}?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('total-leaves').textContent = data.stats.total_leaves;
                    document.getElementById('approved-leaves').textContent = data.stats.approved_leaves;
                    document.getElementById('pending-leaves').textContent = data.stats.pending_leaves;
                    document.getElementById('rejected-leaves').textContent = data.stats.rejected_leaves;
                    document.getElementById('total-days').textContent = data.stats.total_days;
                    document.getElementById('avg-duration').textContent = data.stats.avg_duration.toFixed(1) + ' يوم';
                }
            })
            .catch(error => {
                console.error('Error updating monthly stats:', error);
            });
    }
    
    function showLeaveDetails(leaveId) {
        showLoading();
        
        fetch(`{% url 'Hr:api_get_leave_request_details' 0 %}`.replace('0', leaveId))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderLeaveDetails(data.request);
                    document.getElementById('leave-details-modal').classList.remove('hidden');
                } else {
                    throw new Error(data.message || 'فشل في تحميل تفاصيل الإجازة');
                }
            })
            .catch(error => {
                console.error('Error loading leave details:', error);
                HRComponents.Toast.show(error.message || 'فشل في تحميل تفاصيل الإجازة', 'error');
            })
            .finally(() => {
                hideLoading();
            });
    }
    
    function renderLeaveDetails(leave) {
        document.getElementById('leave-details-title').textContent = `إجازة ${leave.employee.full_name}`;
        
        const content = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-neutral-600">الموظف</label>
                        <div class="mt-1 flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span class="text-primary-600 font-semibold">
                                    ${leave.employee.first_name.charAt(0)}${leave.employee.last_name.charAt(0)}
                                </span>
                            </div>
                            <div>
                                <div class="font-medium text-neutral-800">${leave.employee.full_name}</div>
                                <div class="text-sm text-neutral-600">${leave.employee.department || 'بدون قسم'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-neutral-600">نوع الإجازة</label>
                        <div class="mt-1 text-neutral-800">${leave.leave_type.name}</div>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-neutral-600">الفترة</label>
                        <div class="mt-1 text-neutral-800">
                            من ${leave.start_date} إلى ${leave.end_date}
                        </div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="text-sm font-medium text-neutral-600">عدد الأيام</label>
                        <div class="mt-1 text-2xl font-bold text-primary-600">${leave.total_days} يوم</div>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-neutral-600">الحالة</label>
                        <div class="mt-1">
                            <span class="leave-status status-${leave.status}">
                                ${getStatusText(leave.status)}
                            </span>
                        </div>
                    </div>
                    
                    <div>
                        <label class="text-sm font-medium text-neutral-600">تاريخ الطلب</label>
                        <div class="mt-1 text-neutral-800">${leave.created_at}</div>
                    </div>
                </div>
            </div>
            
            ${leave.reason ? `
            <div class="mt-6">
                <label class="text-sm font-medium text-neutral-600">سبب الإجازة</label>
                <div class="mt-2 p-4 bg-neutral-50 rounded-lg text-neutral-800">
                    ${leave.reason}
                </div>
            </div>
            ` : ''}
            
            ${leave.replacement_employee ? `
            <div class="mt-6">
                <label class="text-sm font-medium text-neutral-600">الموظف البديل</label>
                <div class="mt-1 text-neutral-800">${leave.replacement_employee.full_name}</div>
            </div>
            ` : ''}
            
            ${leave.review_notes ? `
            <div class="mt-6">
                <label class="text-sm font-medium text-neutral-600">ملاحظات المراجعة</label>
                <div class="mt-2 p-4 bg-neutral-50 rounded-lg text-neutral-800">
                    ${leave.review_notes}
                </div>
            </div>
            ` : ''}
        `;
        
        document.getElementById('leave-details-content').innerHTML = content;
        
        // Render action buttons
        let actions = '';
        if (leave.status === 'pending') {
            actions = `
                <button class="btn btn-success btn-sm" onclick="approveLeaveFromCalendar('${leave.id}')">
                    <i class="fas fa-check ml-2"></i>
                    موافقة
                </button>
                <button class="btn btn-error btn-sm" onclick="rejectLeaveFromCalendar('${leave.id}')">
                    <i class="fas fa-times ml-2"></i>
                    رفض
                </button>
            `;
        }
        
        document.getElementById('leave-actions').innerHTML = actions;
    }
    
    function getStatusText(status) {
        const statusMap = {
            'pending': 'معلقة',
            'approved': 'موافق عليها',
            'rejected': 'مرفوضة',
            'cancelled': 'ملغاة'
        };
        return statusMap[status] || status;
    }
    
    // Quick actions
    window.showNewLeaveModal = function() {
        // This would open the new leave request modal
        // For now, redirect to the requests page
        window.location.href = '{% url "Hr:leave_requests" %}';
    };
    
    window.exportCalendar = function() {
        const currentDate = calendar.getDate();
        const month = currentDate.getMonth() + 1;
        const year = currentDate.getFullYear();
        
        const params = new URLSearchParams({
            month: month,
            year: year,
            department: document.getElementById('department-filter').value,
            leave_type: document.getElementById('leave-type-filter').value,
            status: document.getElementById('status-filter').value
        });
        
        window.open(`{% url 'Hr:export_leave_calendar' %}?${params.toString()}`, '_blank');
    };
    
    window.printCalendar = function() {
        window.print();
    };
    
    // Leave actions from calendar
    window.approveLeaveFromCalendar = function(leaveId) {
        if (confirm('هل تريد الموافقة على هذا الطلب؟')) {
            showLoading();
            
            fetch('{% url "Hr:api_review_leave_request" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    request_id: leaveId,
                    action: 'approve',
                    review_notes: 'تمت الموافقة من التقويم'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    HRComponents.Toast.show('تمت الموافقة على الطلب', 'success');
                    document.getElementById('leave-details-modal').classList.add('hidden');
                    refreshCalendar();
                } else {
                    throw new Error(data.message || 'فشل في الموافقة على الطلب');
                }
            })
            .catch(error => {
                console.error('Error approving leave:', error);
                HRComponents.Toast.show(error.message || 'فشل في الموافقة على الطلب', 'error');
            })
            .finally(() => {
                hideLoading();
            });
        }
    };
    
    window.rejectLeaveFromCalendar = function(leaveId) {
        const reason = prompt('يرجى إدخال سبب الرفض:');
        if (reason) {
            showLoading();
            
            fetch('{% url "Hr:api_review_leave_request" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    request_id: leaveId,
                    action: 'reject',
                    review_notes: reason
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    HRComponents.Toast.show('تم رفض الطلب', 'success');
                    document.getElementById('leave-details-modal').classList.add('hidden');
                    refreshCalendar();
                } else {
                    throw new Error(data.message || 'فشل في رفض الطلب');
                }
            })
            .catch(error => {
                console.error('Error rejecting leave:', error);
                HRComponents.Toast.show(error.message || 'فشل في رفض الطلب', 'error');
            })
            .finally(() => {
                hideLoading();
            });
        }
    };
});
</script>
{% endblock %}