{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الرواتب - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-money-check-alt me-2"></i>
    {{ title }}
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:payroll_entries_new:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-{% if action == 'create' %}plus{% else %}edit{% endif %} me-2"></i>
            {{ title }}
        </h5>
    </div>
    <div class="card-body">
        <form method="post" id="payrollEntryForm" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row g-3 mb-4">
                <!-- Employee Selection -->
                <div class="col-md-6">
                    <label for="{{ form.employee.id_for_label }}" class="form-label required">{{ form.employee.label }}</label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.employee.help_text %}
                    <div class="form-text">{{ form.employee.help_text }}</div>
                    {% endif %}
                </div>
                
                <!-- Payroll Period Selection -->
                <div class="col-md-6">
                    <label for="{{ form.payroll_period.id_for_label }}" class="form-label required">{{ form.payroll_period.label }}</label>
                    {{ form.payroll_period }}
                    {% if form.payroll_period.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.payroll_period.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.payroll_period.help_text %}
                    <div class="form-text">{{ form.payroll_period.help_text }}</div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row g-3 mb-4">
                <!-- Basic Salary -->
                <div class="col-md-4">
                    <label for="{{ form.basic_salary.id_for_label }}" class="form-label required">{{ form.basic_salary.label }}</label>
                    <div class="input-group">
                        {{ form.basic_salary }}
                        <span class="input-group-text">ج.م</span>
                    </div>
                    {% if form.basic_salary.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.basic_salary.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <!-- Status -->
                <div class="col-md-4">
                    <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                    {{ form.status }}
                    {% if form.status.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.status.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <!-- Payment Date -->
                <div class="col-md-4">
                    <label for="{{ form.payment_date.id_for_label }}" class="form-label">{{ form.payment_date.label }}</label>
                    {{ form.payment_date }}
                    {% if form.payment_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.payment_date.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row g-3 mb-4">
                <!-- Notes -->
                <div class="col-12">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.notes.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="{% url 'hr:payroll_entries_new:list' %}" class="btn btn-light me-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ
                </button>
            </div>
        </form>
    </div>
</div>

{% if action == 'edit' and entry.payroll_details.exists %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list-ul me-2"></i>
            تفاصيل الراتب
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المكون</th>
                        <th>الفئة</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in entry.payroll_details.all %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ detail.salary_component.name }}</td>
                        <td>
                            <span class="badge bg-{% if detail.salary_component.category == 'earning' %}success{% elif detail.salary_component.category == 'deduction' %}danger{% else %}info{% endif %}">
                                {{ detail.salary_component.get_category_display }}
                            </span>
                        </td>
                        <td class="text-{% if detail.salary_component.category == 'earning' %}success{% elif detail.salary_component.category == 'deduction' %}danger{% else %}info{% endif %}">
                            {{ detail.amount|floatformat:2 }} ج.م
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تهيئة محدد الموظف
        $('#{{ form.employee.id_for_label }}').select2({
            placeholder: 'اختر الموظف',
            allowClear: true,
            width: '100%'
        });
        
        // تهيئة محدد فترة الراتب
        $('#{{ form.payroll_period.id_for_label }}').select2({
            placeholder: 'اختر فترة الراتب',
            allowClear: true,
            width: '100%'
        });
        
        // تهيئة محدد التاريخ
        $('#{{ form.payment_date.id_for_label }}').flatpickr({
            dateFormat: "Y-m-d",
            locale: "ar"
        });
    });
</script>
{% endblock %}