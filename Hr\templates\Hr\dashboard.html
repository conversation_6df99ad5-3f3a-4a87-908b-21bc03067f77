{% extends 'Hr/base_hr.html' %}

{% block title %}لوحة تحكم الموارد البشرية - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم الموارد البشرية{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم الموارد البشرية</li>
{% endblock %}

{% block content %}
<!-- KPIs Section -->
<div class="section-spacing">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <div class="card card-stats card-hover text-center">
            <div class="card-body">
                <div class="w-14 h-14 bg-primary-light rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-users text-primary text-2xl"></i>
                </div>
                <div class="text-2xl font-bold text-primary">{{ total_employees }}</div>
                <p class="text-sm text-muted mb-0">إجمالي الموظفين</p>
            </div>
        </div>
        <div class="card card-stats card-hover text-center">
            <div class="card-body">
                <div class="w-14 h-14 bg-success-light rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-user-check text-success text-2xl"></i>
                </div>
                <div class="text-2xl font-bold text-success">{{ active_employees }}</div>
                <p class="text-sm text-muted mb-0">الموظفين النشطين</p>
            </div>
        </div>
        <div class="card card-stats card-hover text-center">
            <div class="card-body">
                <div class="w-14 h-14 bg-warning-light rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-calendar-check text-warning text-2xl"></i>
                </div>
                <div class="text-2xl font-bold text-warning">{{ today_attendance|default:"0" }}</div>
                <p class="text-sm text-muted mb-0">حضور اليوم</p>
            </div>
        </div>
        <div class="card card-stats card-hover text-center">
            <div class="card-body">
                <div class="w-14 h-14 bg-info-light rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-money-bill-wave text-info text-2xl"></i>
                </div>
                <div class="text-2xl font-bold text-info">{{ total_payroll|default:"0" }}</div>
                <p class="text-sm text-muted mb-0">إجمالي الرواتب هذا الشهر</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="section-spacing">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="card">
            <div class="card-header">
                <div class="card-title"><i class="fas fa-chart-pie me-2"></i>توزيع الموظفين حسب الأقسام</div>
            </div>
            <div class="card-body">
                <canvas id="departmentsPieChart" height="180"></canvas>
            </div>
        </div>
        <div class="card">
            <div class="card-header">
                <div class="card-title"><i class="fas fa-chart-line me-2"></i>الحضور الشهري</div>
            </div>
            <div class="card-body">
                <canvas id="attendanceLineChart" height="180"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities Section -->
<div class="section-spacing">
    <div class="card">
        <div class="card-header">
            <div class="card-title"><i class="fas fa-history me-2"></i>الأنشطة الحديثة</div>
        </div>
        <div class="card-body">
            {% if recent_employees %}
                <ul class="list-group list-group-flush">
                    {% for employee in recent_employees|slice:':5' %}
                    <li class="list-group-item d-flex align-items-center justify-between">
                        <div class="d-flex align-items-center gap-3">
                            <div class="w-10 h-10 bg-success-light rounded-full flex items-center justify-center">
                                <i class="fas fa-user-plus text-success"></i>
                            </div>
                            <div>
                                <div class="fw-bold">{{ employee.emp_full_name }}</div>
                                <div class="text-muted text-xs">{{ employee.department.dept_name|default:"بدون قسم" }}</div>
                            </div>
                        </div>
                        <div class="text-muted text-xs">{{ employee.emp_date_hiring|timesince }} مضت</div>
                        <a href="{% url 'Hr:employees:detail' employee.emp_id %}" class="btn btn-outline-primary btn-sm">عرض</a>
                    </li>
                    {% endfor %}
                </ul>
            {% else %}
                <div class="empty-state text-center py-5">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <h6 class="empty-state-title">لا توجد أنشطة حديثة</h6>
                    <p class="empty-state-description">ستظهر هنا آخر العمليات المنجزة</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // بيانات الأقسام (من الباكند)
    const departmentsLabels = {{ departments_labels|safe }};
    const departmentsData = {{ departments_data|safe }};
    const departmentsColors = [
        '#3b82f6', '#22c55e', '#f59e0b', '#ef4444', '#6366f1', '#0ea5e9', '#eab308', '#14b8a6', '#f43f5e', '#a3e635'
    ];
    const ctxPie = document.getElementById('departmentsPieChart').getContext('2d');
    new Chart(ctxPie, {
        type: 'pie',
        data: {
            labels: departmentsLabels,
            datasets: [{
                data: departmentsData,
                backgroundColor: departmentsColors,
            }]
        },
        options: {
            plugins: {
                legend: { position: 'bottom', labels: { font: { family: 'Cairo' } } }
            }
        }
    });

    // بيانات الحضور الشهري (من الباكند)
    const attendanceLabels = {{ attendance_labels|safe }};
    const attendanceData = {{ attendance_data|safe }};
    const ctxLine = document.getElementById('attendanceLineChart').getContext('2d');
    new Chart(ctxLine, {
        type: 'line',
        data: {
            labels: attendanceLabels,
            datasets: [{
                label: 'الحضور',
                data: attendanceData,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59,130,246,0.1)',
                tension: 0.4,
                pointRadius: 4,
                pointBackgroundColor: '#3b82f6',
                fill: true,
            }]
        },
        options: {
            plugins: {
                legend: { display: false },
            },
            scales: {
                x: { ticks: { font: { family: 'Cairo' } } },
                y: { beginAtZero: true, ticks: { font: { family: 'Cairo' } } }
            }
        }
    });
</script>
{% endblock %}
