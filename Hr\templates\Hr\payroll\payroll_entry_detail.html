{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تفاصيل مدخل الراتب - نظام الرواتب - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-money-check-alt me-2"></i>
    تفاصيل مدخل الراتب
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:payroll_entries_new:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
        
        {% if perms.Hr.change_payrollentry and entry.status == 'draft' %}
        <a href="{% url 'hr:payroll_entries_new:edit' entry.id %}" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i>
            تعديل
        </a>
        {% endif %}
        
        {% if perms.Hr.approve_payrollentry and entry.status == 'draft' %}
        <a href="{% url 'hr:payroll_entries_new:approve' entry.id %}" class="btn btn-outline-success">
            <i class="fas fa-check-circle"></i>
            موافقة
        </a>
        {% endif %}
        
        {% if perms.Hr.reject_payrollentry and entry.status == 'draft' %}
        <a href="{% url 'hr:payroll_entries_new:reject' entry.id %}" class="btn btn-outline-danger">
            <i class="fas fa-times-circle"></i>
            رفض
        </a>
        {% endif %}
        
        {% if perms.Hr.mark_paid_payrollentry and entry.status == 'approved' %}
        <a href="{% url 'hr:payroll_entries_new:mark_paid' entry.id %}" class="btn btn-outline-info">
            <i class="fas fa-money-bill-wave"></i>
            تسجيل الدفع
        </a>
        {% endif %}
        
        <a href="#" onclick="window.print();" class="btn btn-outline-dark">
            <i class="fas fa-print"></i>
            طباعة
        </a>
    </div>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات مدخل الراتب
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الموظف</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">الموظف:</th>
                        <td>{{ entry.employee.get_full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوظيفي:</th>
                        <td>{{ entry.employee.employee_id }}</td>
                    </tr>
                    <tr>
                        <th>القسم:</th>
                        <td>{{ entry.employee.department.name|default:'-' }}</td>
                    </tr>
                    <tr>
                        <th>المسمى الوظيفي:</th>
                        <td>{{ entry.employee.designation.name|default:'-' }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الراتب</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">فترة الراتب:</th>
                        <td>{{ entry.payroll_period.name }}</td>
                    </tr>
                    <tr>
                        <th>الحالة:</th>
                        <td>
                            <span class="badge bg-{% if entry.status == 'draft' %}secondary{% elif entry.status == 'approved' %}success{% elif entry.status == 'rejected' %}danger{% elif entry.status == 'paid' %}primary{% endif %}">
                                {{ entry.get_status_display }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <th>تاريخ الإنشاء:</th>
                        <td>{{ entry.created_at|date:"Y-m-d" }}</td>
                    </tr>
                    {% if entry.payment_date %}
                    <tr>
                        <th>تاريخ الدفع:</th>
                        <td>{{ entry.payment_date|date:"Y-m-d" }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-calculator me-2"></i>
            تفاصيل الراتب
        </h5>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h6 class="card-title">الراتب الأساسي</h6>
                        <h4 class="mb-0">{{ entry.basic_salary|floatformat:2 }} ج.م</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">إجمالي المستحقات</h6>
                        <h4 class="mb-0">{{ entry.total_earnings|floatformat:2 }} ج.م</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">إجمالي الخصومات</h6>
                        <h4 class="mb-0">{{ entry.total_deductions|floatformat:2 }} ج.م</h4>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">صافي الراتب</h5>
                        <h3 class="mb-0">{{ entry.net_salary|floatformat:2 }} ج.م</h3>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h6 class="fw-bold">المستحقات</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المكون</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in entry.payroll_details.all %}
                            {% if detail.salary_component.category == 'earning' %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ detail.salary_component.name }}</td>
                                <td class="text-success">{{ detail.amount|floatformat:2 }} ج.م</td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="fw-bold">
                                <td colspan="2" class="text-end">الإجمالي:</td>
                                <td class="text-success">{{ entry.total_earnings|floatformat:2 }} ج.م</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">الخصومات</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المكون</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in entry.payroll_details.all %}
                            {% if detail.salary_component.category == 'deduction' %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ detail.salary_component.name }}</td>
                                <td class="text-danger">{{ detail.amount|floatformat:2 }} ج.م</td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="fw-bold">
                                <td colspan="2" class="text-end">الإجمالي:</td>
                                <td class="text-danger">{{ entry.total_deductions|floatformat:2 }} ج.م</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% if entry.notes or entry.rejection_reason or entry.approval_notes or entry.payment_notes %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-sticky-note me-2"></i>
            الملاحظات
        </h5>
    </div>
    <div class="card-body">
        {% if entry.notes %}
        <div class="mb-3">
            <h6 class="fw-bold">ملاحظات عامة:</h6>
            <p>{{ entry.notes }}</p>
        </div>
        {% endif %}
        
        {% if entry.approval_notes %}
        <div class="mb-3">
            <h6 class="fw-bold">ملاحظات الموافقة:</h6>
            <p>{{ entry.approval_notes }}</p>
        </div>
        {% endif %}
        
        {% if entry.rejection_reason %}
        <div class="mb-3">
            <h6 class="fw-bold">سبب الرفض:</h6>
            <p class="text-danger">{{ entry.rejection_reason }}</p>
        </div>
        {% endif %}
        
        {% if entry.payment_notes %}
        <div class="mb-3">
            <h6 class="fw-bold">ملاحظات الدفع:</h6>
            <p>{{ entry.payment_notes }}</p>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-history me-2"></i>
            سجل التغييرات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الإجراء</th>
                        <th>بواسطة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ entry.created_at|date:"Y-m-d H:i" }}</td>
                        <td>إنشاء</td>
                        <td>{{ entry.created_by.get_full_name }}</td>
                        <td>-</td>
                    </tr>
                    {% if entry.updated_at and entry.updated_by %}
                    <tr>
                        <td>{{ entry.updated_at|date:"Y-m-d H:i" }}</td>
                        <td>تعديل</td>
                        <td>{{ entry.updated_by.get_full_name }}</td>
                        <td>-</td>
                    </tr>
                    {% endif %}
                    {% if entry.approved_at and entry.approved_by %}
                    <tr>
                        <td>{{ entry.approved_at|date:"Y-m-d H:i" }}</td>
                        <td>موافقة</td>
                        <td>{{ entry.approved_by.get_full_name }}</td>
                        <td>{{ entry.approval_notes|default:'-' }}</td>
                    </tr>
                    {% endif %}
                    {% if entry.rejected_at and entry.rejected_by %}
                    <tr>
                        <td>{{ entry.rejected_at|date:"Y-m-d H:i" }}</td>
                        <td>رفض</td>
                        <td>{{ entry.rejected_by.get_full_name }}</td>
                        <td>{{ entry.rejection_reason|default:'-' }}</td>
                    </tr>
                    {% endif %}
                    {% if entry.payment_date and entry.paid_by %}
                    <tr>
                        <td>{{ entry.payment_date|date:"Y-m-d" }}</td>
                        <td>دفع</td>
                        <td>{{ entry.paid_by.get_full_name }}</td>
                        <td>{{ entry.payment_notes|default:'-' }}</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style media="print">
    @page {
        size: A4;
        margin: 1cm;
    }
    body {
        font-size: 12pt;
    }
    .no-print, .no-print * {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd;
        margin-bottom: 20px;
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
        padding: 10px;
    }
    .card-body {
        padding: 15px;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    .table th {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}