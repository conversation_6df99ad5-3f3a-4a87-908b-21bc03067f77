{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}الموافقة على مدخل الراتب - نظام الرواتب - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-check-circle me-2"></i>
    الموافقة على مدخل الراتب
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:payroll_entries_new:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
        <a href="{% url 'hr:payroll_entries_new:detail' entry.id %}" class="btn btn-outline-primary">
            <i class="fas fa-eye"></i>
            عرض التفاصيل
        </a>
    </div>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-check-circle me-2"></i>
            الموافقة على مدخل الراتب
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            أنت على وشك الموافقة على مدخل الراتب هذا. بعد الموافقة، سيتم تحديث حالة المدخل إلى "موافق عليه" وسيكون جاهزًا للدفع.
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الموظف</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">الموظف:</th>
                        <td>{{ entry.employee.get_full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوظيفي:</th>
                        <td>{{ entry.employee.employee_id }}</td>
                    </tr>
                    <tr>
                        <th>القسم:</th>
                        <td>{{ entry.employee.department.name|default:'-' }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الراتب</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">فترة الراتب:</th>
                        <td>{{ entry.payroll_period.name }}</td>
                    </tr>
                    <tr>
                        <th>الراتب الأساسي:</th>
                        <td>{{ entry.basic_salary|floatformat:2 }} ج.م</td>
                    </tr>
                    <tr>
                        <th>إجمالي الراتب:</th>
                        <td>{{ entry.total_salary|floatformat:2 }} ج.م</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="fw-bold">تفاصيل الراتب</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المكون</th>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in entry.payroll_details.all %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ detail.salary_component.name }}</td>
                                <td>
                                    <span class="badge bg-{% if detail.salary_component.category == 'earning' %}success{% elif detail.salary_component.category == 'deduction' %}danger{% else %}info{% endif %}">
                                        {{ detail.salary_component.get_category_display }}
                                    </span>
                                </td>
                                <td class="text-{% if detail.salary_component.category == 'earning' %}success{% elif detail.salary_component.category == 'deduction' %}danger{% else %}info{% endif %}">
                                    {{ detail.amount|floatformat:2 }} ج.م
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="fw-bold">
                                <td colspan="3" class="text-end">إجمالي المستحقات:</td>
                                <td class="text-success">{{ entry.total_earnings|floatformat:2 }} ج.م</td>
                            </tr>
                            <tr class="fw-bold">
                                <td colspan="3" class="text-end">إجمالي الخصومات:</td>
                                <td class="text-danger">{{ entry.total_deductions|floatformat:2 }} ج.م</td>
                            </tr>
                            <tr class="fw-bold">
                                <td colspan="3" class="text-end">صافي الراتب:</td>
                                <td>{{ entry.net_salary|floatformat:2 }} ج.م</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="approval_notes" class="form-label">ملاحظات الموافقة (اختياري)</label>
                <textarea id="approval_notes" name="approval_notes" class="form-control" rows="3"></textarea>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="{% url 'hr:payroll_entries_new:detail' entry.id %}" class="btn btn-light me-2">إلغاء</a>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check-circle me-1"></i>
                    الموافقة على الراتب
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}