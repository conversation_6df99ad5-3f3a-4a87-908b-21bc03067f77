# Generated by Django 5.0.14 on 2025-07-23 23:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_remove_users_login_new_isactive'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.AlterField(
            model_name='users_login_new',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='users_login_new_groups', to='auth.group', verbose_name='groups'),
        ),
        migrations.AlterField(
            model_name='users_login_new',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='users_login_new_permissions', to='auth.permission', verbose_name='user permissions'),
        ),
    ]
