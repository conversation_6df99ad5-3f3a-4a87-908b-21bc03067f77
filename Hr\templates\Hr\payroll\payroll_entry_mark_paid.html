{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تسجيل دفع الراتب - نظام الرواتب - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-money-bill-wave me-2"></i>
    تسجيل دفع الراتب
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:payroll_entries_new:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
        <a href="{% url 'hr:payroll_entries_new:detail' entry.id %}" class="btn btn-outline-primary">
            <i class="fas fa-eye"></i>
            عرض التفاصيل
        </a>
    </div>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-money-bill-wave me-2"></i>
            تسجيل دفع الراتب
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            أنت على وشك تسجيل دفع هذا الراتب. بعد التأكيد، سيتم تحديث حالة المدخل إلى "مدفوع" وسيتم تسجيل تاريخ الدفع.
        </div>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الموظف</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">الموظف:</th>
                        <td>{{ entry.employee.get_full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوظيفي:</th>
                        <td>{{ entry.employee.employee_id }}</td>
                    </tr>
                    <tr>
                        <th>القسم:</th>
                        <td>{{ entry.employee.department.name|default:'-' }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">معلومات الراتب</h6>
                <table class="table table-sm">
                    <tr>
                        <th style="width: 40%">فترة الراتب:</th>
                        <td>{{ entry.payroll_period.name }}</td>
                    </tr>
                    <tr>
                        <th>الراتب الأساسي:</th>
                        <td>{{ entry.basic_salary|floatformat:2 }} ج.م</td>
                    </tr>
                    <tr>
                        <th>إجمالي الراتب:</th>
                        <td>{{ entry.total_salary|floatformat:2 }} ج.م</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="fw-bold">ملخص الراتب</h6>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>البند</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>إجمالي المستحقات</td>
                                <td class="text-success">{{ entry.total_earnings|floatformat:2 }} ج.م</td>
                            </tr>
                            <tr>
                                <td>إجمالي الخصومات</td>
                                <td class="text-danger">{{ entry.total_deductions|floatformat:2 }} ج.م</td>
                            </tr>
                            <tr class="fw-bold">
                                <td>صافي الراتب</td>
                                <td>{{ entry.net_salary|floatformat:2 }} ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            
            <div class="row g-3 mb-4">
                <div class="col-md-6">
                    <label for="payment_date" class="form-label required">تاريخ الدفع</label>
                    <input type="date" id="payment_date" name="payment_date" class="form-control" value="{% now 'Y-m-d' %}" required>
                </div>
                
                <div class="col-md-6">
                    <label for="payment_method" class="form-label required">طريقة الدفع</label>
                    <select id="payment_method" name="payment_method" class="form-select" required>
                        <option value="">اختر طريقة الدفع</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="cash">نقدي</option>
                        <option value="cheque">شيك</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="payment_reference" class="form-label">رقم المرجع (اختياري)</label>
                <input type="text" id="payment_reference" name="payment_reference" class="form-control" placeholder="مثال: رقم الشيك، رقم التحويل البنكي">
            </div>
            
            <div class="mb-3">
                <label for="payment_notes" class="form-label">ملاحظات الدفع (اختياري)</label>
                <textarea id="payment_notes" name="payment_notes" class="form-control" rows="3"></textarea>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="{% url 'hr:payroll_entries_new:detail' entry.id %}" class="btn btn-light me-2">إلغاء</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-money-bill-wave me-1"></i>
                    تأكيد الدفع
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تهيئة محدد طريقة الدفع
        $('#payment_method').select2({
            placeholder: 'اختر طريقة الدفع',
            width: '100%'
        });
        
        // تهيئة محدد التاريخ
        $('#payment_date').flatpickr({
            dateFormat: "Y-m-d",
            locale: "ar",
            defaultDate: "today"
        });
    });
</script>
{% endblock %}