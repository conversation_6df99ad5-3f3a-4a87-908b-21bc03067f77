{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تقارير الموظفين{% endblock %}
{% block page_title %}تقارير الموظفين{% endblock %}

{% block breadcrumb_items %}
<div class="breadcrumb-item">
    <a href="{% url 'Hr:analytics_dashboard' %}" class="breadcrumb-link">التحليلات والتقارير</a>
</div>
<div class="breadcrumb-item">
    <span class="breadcrumb-link">تقارير الموظفين</span>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .report-builder {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
        margin-bottom: var(--space-6);
    }
    
    .report-builder-header {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
        padding: var(--space-6);
    }
    
    .report-builder-title {
        font-size: var(--text-2xl);
        font-weight: var(--font-semibold);
        margin-bottom: var(--space-2);
    }
    
    .report-builder-subtitle {
        opacity: 0.9;
    }
    
    .report-builder-body {
        padding: var(--space-6);
    }
    
    .report-section {
        margin-bottom: var(--space-8);
    }
    
    .report-section:last-child {
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        margin-bottom: var(--space-4);
        display: flex;
        align-items: center;
        gap: var(--space-2);
    }
    
    .section-icon {
        width: 24px;
        height: 24px;
        border-radius: var(--radius-full);
        background-color: var(--primary-100);
        color: var(--primary-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-sm);
    }
    
    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
    }
    
    .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-3);
    }
    
    .checkbox-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2);
        border-radius: var(--radius-md);
        transition: all var(--transition-fast);
    }
    
    .checkbox-item:hover {
        background-color: var(--neutral-50);
    }
    
    .report-templates {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }
    
    .template-card {
        background: white;
        border: 2px solid var(--neutral-200);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        cursor: pointer;
        transition: all var(--transition-fast);
    }
    
    .template-card:hover {
        border-color: var(--primary-300);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .template-card.selected {
        border-color: var(--primary-600);
        background-color: var(--primary-50);
    }
    
    .template-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: var(--space-4);
        font-size: var(--text-xl);
    }
    
    .template-title {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        margin-bottom: var(--space-2);
    }
    
    .template-description {
        color: var(--neutral-600);
        font-size: var(--text-sm);
        line-height: 1.5;
    }
    
    .report-preview {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
    }
    
    .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-6);
        padding-bottom: var(--space-4);
        border-bottom: 2px solid var(--neutral-100);
    }
    
    .preview-title {
        font-size: var(--text-xl);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
    }
    
    .preview-actions {
        display: flex;
        gap: var(--space-3);
    }
    
    .report-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);
        padding: var(--space-4);
        background-color: var(--neutral-50);
        border-radius: var(--radius-lg);
    }
    
    .summary-item {
        text-align: center;
    }
    
    .summary-value {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        color: var(--primary-600);
        margin-bottom: var(--space-1);
    }
    
    .summary-label {
        font-size: var(--text-sm);
        color: var(--neutral-600);
    }
    
    .report-table {
        background: white;
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }
    
    .report-table th {
        background-color: var(--neutral-100);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        padding: var(--space-4);
    }
    
    .report-table td {
        padding: var(--space-4);
        border-bottom: 1px solid var(--neutral-100);
    }
    
    .report-table tr:last-child td {
        border-bottom: none;
    }
    
    .employee-photo {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .employee-placeholder {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background-color: var(--primary-100);
        color: var(--primary-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: var(--font-semibold);
        font-size: var(--text-sm);
    }
    
    .status-badge {
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: var(--font-medium);
    }
    
    .status-active {
        background-color: var(--success-100);
        color: var(--success-700);
    }
    
    .status-inactive {
        background-color: var(--error-100);
        color: var(--error-700);
    }
    
    .status-probation {
        background-color: var(--warning-100);
        color: var(--warning-700);
    }
    
    @media (max-width: 768px) {
        .filter-grid {
            grid-template-columns: 1fr;
        }
        
        .checkbox-grid {
            grid-template-columns: 1fr;
        }
        
        .report-templates {
            grid-template-columns: 1fr;
        }
        
        .preview-header {
            flex-direction: column;
            gap: var(--space-4);
            align-items: stretch;
        }
        
        .preview-actions {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Report Templates -->
<div class="mb-6">
    <h2 class="text-xl font-semibold text-neutral-800 mb-4">قوالب التقارير الجاهزة</h2>
    
    <div class="report-templates">
        <div class="template-card" data-template="comprehensive" onclick="selectTemplate('comprehensive')">
            <div class="template-icon bg-primary-100 text-primary-600">
                <i class="fas fa-users"></i>
            </div>
            <div class="template-title">تقرير شامل للموظفين</div>
            <div class="template-description">تقرير مفصل يشمل جميع بيانات الموظفين الأساسية والوظيفية</div>
        </div>
        
        <div class="template-card" data-template="organizational" onclick="selectTemplate('organizational')">
            <div class="template-icon bg-success-100 text-success-600">
                <i class="fas fa-sitemap"></i>
            </div>
            <div class="template-title">الهيكل التنظيمي</div>
            <div class="template-description">عرض الموظفين مرتبين حسب الأقسام والمناصب الوظيفية</div>
        </div>
        
        <div class="template-card" data-template="birthdays" onclick="selectTemplate('birthdays')">
            <div class="template-icon bg-warning-100 text-warning-600">
                <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="template-title">أعياد الميلاد والذكريات</div>
            <div class="template-description">قائمة بأعياد ميلاد الموظفين وذكريات التوظيف</div>
        </div>
        
        <div class="template-card" data-template="new-joiners" onclick="selectTemplate('new-joiners')">
            <div class="template-icon bg-error-100 text-error-600">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="template-title">الموظفون الجدد والمغادرون</div>
            <div class="template-description">تقرير بالموظفين الجدد والذين غادروا خلال فترة محددة</div>
        </div>
        
        <div class="template-card" data-template="custom" onclick="selectTemplate('custom')">
            <div class="template-icon bg-purple-100 text-purple-600">
                <i class="fas fa-cog"></i>
            </div>
            <div class="template-title">تقرير مخصص</div>
            <div class="template-description">إنشاء تقرير مخصص باختيار الحقول والمعايير المطلوبة</div>
        </div>
    </div>
</div>

<!-- Report Builder -->
<div class="report-builder" id="report-builder" style="display: none;">
    <div class="report-builder-header">
        <h2 class="report-builder-title">منشئ التقارير</h2>
        <p class="report-builder-subtitle">اختر المعايير والحقول لإنشاء تقرير مخصص</p>
    </div>
    
    <div class="report-builder-body">
        <!-- Filters Section -->
        <div class="report-section">
            <h3 class="section-title">
                <div class="section-icon">
                    <i class="fas fa-filter"></i>
                </div>
                معايير التصفية
            </h3>
            
            <div class="filter-grid">
                <div class="form-group">
                    <label for="department-filter" class="form-label">القسم</label>
                    <select id="department-filter" class="form-select" multiple>
                        {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="position-filter" class="form-label">المنصب</label>
                    <select id="position-filter" class="form-select" multiple>
                        {% for position in positions %}
                            <option value="{{ position.id }}">{{ position.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="employment-status" class="form-label">حالة التوظيف</label>
                    <select id="employment-status" class="form-select" multiple>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="probation">تحت التجربة</option>
                        <option value="terminated">منتهي الخدمة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="hire-date-from" class="form-label">تاريخ التوظيف من</label>
                    <input type="date" id="hire-date-from" class="form-input">
                </div>
                
                <div class="form-group">
                    <label for="hire-date-to" class="form-label">تاريخ التوظيف إلى</label>
                    <input type="date" id="hire-date-to" class="form-input">
                </div>
                
                <div class="form-group">
                    <label for="age-range" class="form-label">الفئة العمرية</label>
                    <select id="age-range" class="form-select">
                        <option value="">جميع الأعمار</option>
                        <option value="20-30">20-30 سنة</option>
                        <option value="31-40">31-40 سنة</option>
                        <option value="41-50">41-50 سنة</option>
                        <option value="51-60">51-60 سنة</option>
                        <option value="60+">أكثر من 60 سنة</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Fields Selection -->
        <div class="report-section">
            <h3 class="section-title">
                <div class="section-icon">
                    <i class="fas fa-list"></i>
                </div>
                الحقول المطلوبة
            </h3>
            
            <div class="checkbox-grid">
                <div class="checkbox-item">
                    <input type="checkbox" id="field-photo" class="form-checkbox" checked>
                    <label for="field-photo">الصورة الشخصية</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-name" class="form-checkbox" checked>
                    <label for="field-name">الاسم الكامل</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-employee-number" class="form-checkbox" checked>
                    <label for="field-employee-number">رقم الموظف</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-department" class="form-checkbox" checked>
                    <label for="field-department">القسم</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-position" class="form-checkbox" checked>
                    <label for="field-position">المنصب</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-hire-date" class="form-checkbox">
                    <label for="field-hire-date">تاريخ التوظيف</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-salary" class="form-checkbox">
                    <label for="field-salary">الراتب الأساسي</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-phone" class="form-checkbox">
                    <label for="field-phone">رقم الهاتف</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-email" class="form-checkbox">
                    <label for="field-email">البريد الإلكتروني</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-national-id" class="form-checkbox">
                    <label for="field-national-id">رقم الهوية</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-birth-date" class="form-checkbox">
                    <label for="field-birth-date">تاريخ الميلاد</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="field-status" class="form-checkbox" checked>
                    <label for="field-status">حالة التوظيف</label>
                </div>
            </div>
        </div>
        
        <!-- Sorting Options -->
        <div class="report-section">
            <h3 class="section-title">
                <div class="section-icon">
                    <i class="fas fa-sort"></i>
                </div>
                خيارات الترتيب
            </h3>
            
            <div class="filter-grid">
                <div class="form-group">
                    <label for="sort-by" class="form-label">ترتيب حسب</label>
                    <select id="sort-by" class="form-select">
                        <option value="name">الاسم</option>
                        <option value="employee_number">رقم الموظف</option>
                        <option value="department">القسم</option>
                        <option value="hire_date">تاريخ التوظيف</option>
                        <option value="salary">الراتب</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="sort-order" class="form-label">اتجاه الترتيب</label>
                    <select id="sort-order" class="form-select">
                        <option value="asc">تصاعدي</option>
                        <option value="desc">تنازلي</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Generate Report Button -->
        <div class="flex justify-center">
            <button class="btn btn-primary btn-lg" id="generate-report">
                <i class="fas fa-chart-bar ml-2"></i>
                إنشاء التقرير
            </button>
        </div>
    </div>
</div><!-- R
eport Preview -->
<div class="report-preview" id="report-preview" style="display: none;">
    <div class="preview-header">
        <h3 class="preview-title" id="report-title">معاينة التقرير</h3>
        <div class="preview-actions">
            <button class="btn btn-outline btn-sm" id="edit-report">
                <i class="fas fa-edit ml-2"></i>
                تعديل
            </button>
            <button class="btn btn-secondary btn-sm" id="export-excel">
                <i class="fas fa-file-excel ml-2"></i>
                Excel
            </button>
            <button class="btn btn-error btn-sm" id="export-pdf">
                <i class="fas fa-file-pdf ml-2"></i>
                PDF
            </button>
            <button class="btn btn-primary btn-sm" id="save-report">
                <i class="fas fa-save ml-2"></i>
                حفظ التقرير
            </button>
        </div>
    </div>
    
    <!-- Report Summary -->
    <div class="report-summary" id="report-summary">
        <!-- Will be populated by JavaScript -->
    </div>
    
    <!-- Report Table -->
    <div class="report-table">
        <div class="table-container">
            <table class="table" id="report-data-table">
                <thead id="report-table-head">
                    <!-- Will be populated by JavaScript -->
                </thead>
                <tbody id="report-table-body">
                    <!-- Will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="flex justify-center mt-6" id="report-pagination">
        <!-- Will be populated by JavaScript -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedTemplate = null;
    let reportData = null;
    let currentPage = 1;
    const itemsPerPage = 50;
    
    // Template selection
    window.selectTemplate = function(template) {
        // Remove previous selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Select new template
        document.querySelector(`[data-template="${template}"]`).classList.add('selected');
        selectedTemplate = template;
        
        if (template === 'custom') {
            document.getElementById('report-builder').style.display = 'block';
            document.getElementById('report-preview').style.display = 'none';
        } else {
            generatePredefinedReport(template);
        }
    };
    
    // Generate predefined reports
    function generatePredefinedReport(template) {
        showLoading();
        
        fetch('{% url "Hr:api_generate_employee_report" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                template: template
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                reportData = data.report_data;
                displayReport(data.report_data);
                document.getElementById('report-builder').style.display = 'none';
                document.getElementById('report-preview').style.display = 'block';
            } else {
                throw new Error(data.message || 'فشل في إنشاء التقرير');
            }
        })
        .catch(error => {
            console.error('Error generating report:', error);
            HRComponents.Toast.show(error.message || 'فشل في إنشاء التقرير', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    // Generate custom report
    document.getElementById('generate-report').addEventListener('click', function() {
        const filters = collectFilters();
        const fields = collectFields();
        const sorting = collectSorting();
        
        if (fields.length === 0) {
            HRComponents.Toast.show('يرجى اختيار حقل واحد على الأقل', 'error');
            return;
        }
        
        showLoading();
        
        fetch('{% url "Hr:api_generate_custom_employee_report" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                filters: filters,
                fields: fields,
                sorting: sorting
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                reportData = data.report_data;
                displayReport(data.report_data);
                document.getElementById('report-preview').style.display = 'block';
            } else {
                throw new Error(data.message || 'فشل في إنشاء التقرير');
            }
        })
        .catch(error => {
            console.error('Error generating custom report:', error);
            HRComponents.Toast.show(error.message || 'فشل في إنشاء التقرير', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    function collectFilters() {
        return {
            departments: Array.from(document.getElementById('department-filter').selectedOptions).map(option => option.value),
            positions: Array.from(document.getElementById('position-filter').selectedOptions).map(option => option.value),
            employment_status: Array.from(document.getElementById('employment-status').selectedOptions).map(option => option.value),
            hire_date_from: document.getElementById('hire-date-from').value,
            hire_date_to: document.getElementById('hire-date-to').value,
            age_range: document.getElementById('age-range').value
        };
    }
    
    function collectFields() {
        const fields = [];
        document.querySelectorAll('.checkbox-item input[type="checkbox"]:checked').forEach(checkbox => {
            fields.push(checkbox.id.replace('field-', ''));
        });
        return fields;
    }
    
    function collectSorting() {
        return {
            sort_by: document.getElementById('sort-by').value,
            sort_order: document.getElementById('sort-order').value
        };
    }
    
    function displayReport(data) {
        // Update report title
        document.getElementById('report-title').textContent = data.title;
        
        // Display summary
        displaySummary(data.summary);
        
        // Display table
        displayTable(data.headers, data.rows);
        
        // Display pagination
        displayPagination(data.rows.length);
    }
    
    function displaySummary(summary) {
        const summaryContainer = document.getElementById('report-summary');
        
        const summaryHTML = `
            <div class="summary-item">
                <div class="summary-value">${summary.total_employees}</div>
                <div class="summary-label">إجمالي الموظفين</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">${summary.active_employees}</div>
                <div class="summary-label">الموظفون النشطون</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">${summary.departments_count}</div>
                <div class="summary-label">عدد الأقسام</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">${summary.avg_age}</div>
                <div class="summary-label">متوسط العمر</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">${summary.avg_experience}</div>
                <div class="summary-label">متوسط سنوات الخبرة</div>
            </div>
        `;
        
        summaryContainer.innerHTML = summaryHTML;
    }
    
    function displayTable(headers, rows) {
        const thead = document.getElementById('report-table-head');
        const tbody = document.getElementById('report-table-body');
        
        // Generate table headers
        const headerHTML = '<tr>' + headers.map(header => `<th>${header}</th>`).join('') + '</tr>';
        thead.innerHTML = headerHTML;
        
        // Generate table rows (paginated)
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedRows = rows.slice(startIndex, endIndex);
        
        const rowsHTML = paginatedRows.map(row => {
            return '<tr>' + row.map((cell, index) => {
                // Special formatting for certain columns
                if (headers[index] === 'الصورة الشخصية') {
                    return `<td>${cell ? `<img src="${cell}" class="employee-photo" alt="صورة الموظف">` : '<div class="employee-placeholder">-</div>'}</td>`;
                } else if (headers[index] === 'حالة التوظيف') {
                    const statusClass = cell === 'نشط' ? 'status-active' : cell === 'غير نشط' ? 'status-inactive' : 'status-probation';
                    return `<td><span class="status-badge ${statusClass}">${cell}</span></td>`;
                } else if (headers[index] === 'الراتب الأساسي') {
                    return `<td class="text-right">${cell ? parseFloat(cell).toLocaleString('ar-SA') + ' ريال' : '-'}</td>`;
                } else {
                    return `<td>${cell || '-'}</td>`;
                }
            }).join('') + '</tr>';
        }).join('');
        
        tbody.innerHTML = rowsHTML;
    }
    
    function displayPagination(totalRows) {
        const totalPages = Math.ceil(totalRows / itemsPerPage);
        const paginationContainer = document.getElementById('report-pagination');
        
        if (totalPages <= 1) {
            paginationContainer.innerHTML = '';
            return;
        }
        
        let paginationHTML = '<nav class="pagination">';
        
        // Previous button
        if (currentPage > 1) {
            paginationHTML += `<button class="pagination-link" onclick="changePage(${currentPage - 1})">السابقة</button>`;
        }
        
        // Page numbers
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const activeClass = i === currentPage ? 'active' : '';
            paginationHTML += `<button class="pagination-link ${activeClass}" onclick="changePage(${i})">${i}</button>`;
        }
        
        // Next button
        if (currentPage < totalPages) {
            paginationHTML += `<button class="pagination-link" onclick="changePage(${currentPage + 1})">التالية</button>`;
        }
        
        paginationHTML += '</nav>';
        paginationContainer.innerHTML = paginationHTML;
    }
    
    window.changePage = function(page) {
        currentPage = page;
        if (reportData) {
            displayTable(reportData.headers, reportData.rows);
            displayPagination(reportData.rows.length);
        }
    };
    
    // Edit report
    document.getElementById('edit-report').addEventListener('click', function() {
        document.getElementById('report-builder').style.display = 'block';
        document.getElementById('report-preview').style.display = 'none';
    });
    
    // Export functions
    document.getElementById('export-excel').addEventListener('click', function() {
        exportReport('excel');
    });
    
    document.getElementById('export-pdf').addEventListener('click', function() {
        exportReport('pdf');
    });
    
    function exportReport(format) {
        if (!reportData) {
            HRComponents.Toast.show('لا يوجد تقرير للتصدير', 'error');
            return;
        }
        
        showLoading();
        
        fetch('{% url "Hr:api_export_employee_report" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                report_data: reportData,
                format: format
            })
        })
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `employee-report-${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            HRComponents.Toast.show('تم تصدير التقرير بنجاح', 'success');
        })
        .catch(error => {
            console.error('Error exporting report:', error);
            HRComponents.Toast.show('فشل في تصدير التقرير', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    // Save report
    document.getElementById('save-report').addEventListener('click', function() {
        if (!reportData) {
            HRComponents.Toast.show('لا يوجد تقرير للحفظ', 'error');
            return;
        }
        
        const reportName = prompt('أدخل اسم التقرير:');
        if (!reportName) return;
        
        showLoading();
        
        fetch('{% url "Hr:api_save_employee_report" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                name: reportName,
                template: selectedTemplate,
                filters: selectedTemplate === 'custom' ? collectFilters() : {},
                fields: selectedTemplate === 'custom' ? collectFields() : [],
                sorting: selectedTemplate === 'custom' ? collectSorting() : {},
                report_data: reportData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show('تم حفظ التقرير بنجاح', 'success');
            } else {
                throw new Error(data.message || 'فشل في حفظ التقرير');
            }
        })
        .catch(error => {
            console.error('Error saving report:', error);
            HRComponents.Toast.show(error.message || 'فشل في حفظ التقرير', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    // Initialize multi-select dropdowns
    initializeMultiSelect();
    
    function initializeMultiSelect() {
        const multiSelects = document.querySelectorAll('select[multiple]');
        multiSelects.forEach(select => {
            // Add styling for better UX
            select.style.height = 'auto';
            select.style.minHeight = '120px';
        });
    }
});
</script>
{% endblock %}