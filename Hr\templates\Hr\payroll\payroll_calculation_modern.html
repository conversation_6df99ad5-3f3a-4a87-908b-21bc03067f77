{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}حساب الرواتب{% endblock %}
{% block page_title %}حساب الرواتب{% endblock %}

{% block breadcrumb_items %}
<div class="breadcrumb-item">
    <a href="{% url 'Hr:payroll_dashboard' %}" class="breadcrumb-link">الرواتب</a>
</div>
<div class="breadcrumb-item">
    <span class="breadcrumb-link">حساب الرواتب</span>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .calculation-wizard {
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
    }
    
    .wizard-header {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        color: white;
        padding: var(--space-6);
    }
    
    .wizard-title {
        font-size: var(--text-2xl);
        font-weight: var(--font-semibold);
        margin-bottom: var(--space-2);
    }
    
    .wizard-description {
        opacity: 0.9;
    }
    
    .wizard-steps {
        display: flex;
        background-color: var(--neutral-50);
        border-bottom: 1px solid var(--neutral-200);
    }
    
    .wizard-step {
        flex: 1;
        padding: var(--space-4);
        text-align: center;
        position: relative;
        cursor: pointer;
        transition: all var(--transition-fast);
    }
    
    .wizard-step.active {
        background-color: white;
        color: var(--primary-600);
    }
    
    .wizard-step.completed {
        background-color: var(--success-50);
        color: var(--success-700);
    }
    
    .wizard-step:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 60%;
        background-color: var(--neutral-300);
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background-color: var(--neutral-300);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-2);
        font-weight: var(--font-semibold);
        font-size: var(--text-sm);
    }
    
    .wizard-step.active .step-number {
        background-color: var(--primary-600);
    }
    
    .wizard-step.completed .step-number {
        background-color: var(--success-600);
    }
    
    .step-title {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
    }
    
    .wizard-content {
        padding: var(--space-6);
    }
    
    .step-content {
        display: none;
    }
    
    .step-content.active {
        display: block;
    }
    
    .employee-selection-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-6);
    }
    
    .employee-card {
        border: 2px solid var(--neutral-200);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        cursor: pointer;
        transition: all var(--transition-fast);
    }
    
    .employee-card:hover {
        border-color: var(--primary-300);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .employee-card.selected {
        border-color: var(--primary-600);
        background-color: var(--primary-50);
    }
    
    .employee-info {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        margin-bottom: var(--space-3);
    }
    
    .employee-avatar {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-full);
        background-color: var(--primary-100);
        color: var(--primary-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: var(--font-semibold);
    }
    
    .employee-details h4 {
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
        margin-bottom: var(--space-1);
    }
    
    .employee-meta {
        font-size: var(--text-sm);
        color: var(--neutral-600);
    }
    
    .employee-salary-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: var(--space-3);
        border-top: 1px solid var(--neutral-200);
    }
    
    .salary-amount {
        font-weight: var(--font-semibold);
        color: var(--success-600);
    }
    
    .calculation-summary {
        background-color: var(--neutral-50);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        margin-bottom: var(--space-6);
    }
    
    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-4);
    }
    
    .summary-item {
        text-align: center;
    }
    
    .summary-value {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        color: var(--neutral-800);
        margin-bottom: var(--space-1);
    }
    
    .summary-label {
        font-size: var(--text-sm);
        color: var(--neutral-600);
    }
    
    .payroll-table {
        background: white;
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }
    
    .payroll-table th {
        background-color: var(--neutral-100);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
    }
    
    .salary-breakdown {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-6);
        margin-bottom: var(--space-6);
    }
    
    .breakdown-section {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-sm);
    }
    
    .breakdown-header {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-4);
        padding-bottom: var(--space-3);
        border-bottom: 2px solid var(--neutral-100);
    }
    
    .breakdown-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-lg);
    }
    
    .earnings-icon {
        background-color: var(--success-100);
        color: var(--success-600);
    }
    
    .deductions-icon {
        background-color: var(--error-100);
        color: var(--error-600);
    }
    
    .breakdown-title {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--neutral-800);
    }
    
    .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-3) 0;
        border-bottom: 1px solid var(--neutral-100);
    }
    
    .breakdown-item:last-child {
        border-bottom: none;
        font-weight: var(--font-semibold);
        background-color: var(--neutral-50);
        margin: var(--space-3) calc(var(--space-3) * -1) calc(var(--space-3) * -1);
        padding: var(--space-3);
        border-radius: var(--radius-md);
    }
    
    .breakdown-label {
        color: var(--neutral-700);
    }
    
    .breakdown-value {
        font-weight: var(--font-medium);
        color: var(--neutral-800);
        direction: ltr;
    }
    
    .wizard-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-6);
        background-color: var(--neutral-50);
        border-top: 1px solid var(--neutral-200);
    }
    
    @media (max-width: 768px) {
        .wizard-steps {
            flex-direction: column;
        }
        
        .wizard-step:not(:last-child)::after {
            display: none;
        }
        
        .salary-breakdown {
            grid-template-columns: 1fr;
        }
        
        .employee-selection-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}{% bl
ock content %}
<div class="calculation-wizard">
    <!-- Wizard Header -->
    <div class="wizard-header">
        <h1 class="wizard-title">حساب الرواتب</h1>
        <p class="wizard-description">احسب رواتب الموظفين للفترة المحددة مع عرض تفصيلي للاستحقاقات والخصومات</p>
    </div>
    
    <!-- Wizard Steps -->
    <div class="wizard-steps">
        <div class="wizard-step active" data-step="1">
            <div class="step-number">1</div>
            <div class="step-title">اختيار الفترة</div>
        </div>
        <div class="wizard-step" data-step="2">
            <div class="step-number">2</div>
            <div class="step-title">اختيار الموظفين</div>
        </div>
        <div class="wizard-step" data-step="3">
            <div class="step-number">3</div>
            <div class="step-title">مراجعة الحسابات</div>
        </div>
        <div class="wizard-step" data-step="4">
            <div class="step-number">4</div>
            <div class="step-title">إنتاج الكشوف</div>
        </div>
    </div>
    
    <!-- Wizard Content -->
    <div class="wizard-content">
        <!-- Step 1: Period Selection -->
        <div class="step-content active" id="step-1">
            <h3 class="text-xl font-semibold text-neutral-800 mb-6">اختيار فترة الراتب</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                    <label for="payroll-month" class="form-label required">الشهر</label>
                    <select id="payroll-month" class="form-select" required>
                        <option value="">اختر الشهر</option>
                        <option value="1">يناير</option>
                        <option value="2">فبراير</option>
                        <option value="3">مارس</option>
                        <option value="4">أبريل</option>
                        <option value="5">مايو</option>
                        <option value="6">يونيو</option>
                        <option value="7">يوليو</option>
                        <option value="8">أغسطس</option>
                        <option value="9">سبتمبر</option>
                        <option value="10">أكتوبر</option>
                        <option value="11">نوفمبر</option>
                        <option value="12">ديسمبر</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="payroll-year" class="form-label required">السنة</label>
                    <select id="payroll-year" class="form-select" required>
                        <option value="">اختر السنة</option>
                        {% for year in available_years %}
                            <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="form-group">
                    <label for="calculation-type" class="form-label required">نوع الحساب</label>
                    <select id="calculation-type" class="form-select" required>
                        <option value="monthly">راتب شهري</option>
                        <option value="bonus">مكافآت</option>
                        <option value="overtime">وقت إضافي</option>
                        <option value="custom">حساب مخصص</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="department-filter" class="form-label">تصفية حسب القسم</label>
                    <select id="department-filter" class="form-select">
                        <option value="">جميع الأقسام</option>
                        {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="alert alert-primary mt-6">
                <div class="alert-title">معلومات مهمة</div>
                <ul class="mt-2 text-sm">
                    <li>• سيتم حساب الراتب بناءً على بيانات الحضور والغياب للفترة المحددة</li>
                    <li>• سيتم تطبيق جميع الاستحقاقات والخصومات المعتمدة</li>
                    <li>• يمكن مراجعة وتعديل الحسابات قبل الإنتاج النهائي</li>
                </ul>
            </div>
        </div>
        
        <!-- Step 2: Employee Selection -->
        <div class="step-content" id="step-2">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-neutral-800">اختيار الموظفين</h3>
                <div class="flex items-center gap-3">
                    <button class="btn btn-outline btn-sm" id="select-all-employees">
                        <i class="fas fa-check-square ml-2"></i>
                        اختيار الكل
                    </button>
                    <button class="btn btn-outline btn-sm" id="clear-selection">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء التحديد
                    </button>
                </div>
            </div>
            
            <div class="form-group mb-6">
                <input type="text" id="employee-search" class="form-input" 
                       placeholder="البحث عن موظف...">
            </div>
            
            <div class="employee-selection-grid" id="employees-grid">
                <!-- Will be populated by JavaScript -->
            </div>
            
            <div class="calculation-summary" id="selection-summary" style="display: none;">
                <h4 class="text-lg font-semibold text-neutral-800 mb-4">ملخص الاختيار</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value" id="selected-count">0</div>
                        <div class="summary-label">موظف محدد</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value" id="estimated-total">0</div>
                        <div class="summary-label">إجمالي الرواتب المتوقع</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value" id="processing-time">-</div>
                        <div class="summary-label">وقت المعالجة المتوقع</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Step 3: Review Calculations -->
        <div class="step-content" id="step-3">
            <h3 class="text-xl font-semibold text-neutral-800 mb-6">مراجعة الحسابات</h3>
            
            <div class="calculation-summary mb-6">
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value text-success-600" id="total-earnings">0</div>
                        <div class="summary-label">إجمالي الاستحقاقات</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value text-error-600" id="total-deductions">0</div>
                        <div class="summary-label">إجمالي الخصومات</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value text-primary-600" id="net-total">0</div>
                        <div class="summary-label">صافي الرواتب</div>
                    </div>
                </div>
            </div>
            
            <div class="payroll-table">
                <div class="table-container">
                    <table class="table" id="payroll-preview-table">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>الراتب الأساسي</th>
                                <th>البدلات</th>
                                <th>الوقت الإضافي</th>
                                <th>إجمالي الاستحقاقات</th>
                                <th>الخصومات</th>
                                <th>صافي الراتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Step 4: Generate Payslips -->
        <div class="step-content" id="step-4">
            <h3 class="text-xl font-semibold text-neutral-800 mb-6">إنتاج كشوف الرواتب</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">خيارات الإنتاج</h4>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">تنسيق الملف</label>
                            <div class="flex flex-col gap-2 mt-2">
                                <label class="flex items-center">
                                    <input type="radio" name="output_format" value="pdf" class="form-radio" checked>
                                    <span class="mr-2">PDF - كشوف فردية</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="output_format" value="excel" class="form-radio">
                                    <span class="mr-2">Excel - جدول موحد</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="output_format" value="both" class="form-radio">
                                    <span class="mr-2">كلاهما</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="flex items-center">
                                <input type="checkbox" id="include-breakdown" class="form-checkbox" checked>
                                <label for="include-breakdown" class="mr-2">تضمين تفاصيل الحسابات</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="flex items-center">
                                <input type="checkbox" id="send-email" class="form-checkbox">
                                <label for="send-email" class="mr-2">إرسال بالبريد الإلكتروني</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">معاينة الكشف</h4>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-file-pdf text-6xl text-error-400 mb-4"></i>
                            <p class="text-neutral-600 mb-4">معاينة كشف الراتب</p>
                            <button class="btn btn-outline btn-sm" id="preview-payslip">
                                <i class="fas fa-eye ml-2"></i>
                                معاينة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-success">
                <div class="alert-title">جاهز للإنتاج</div>
                <p>تم التحقق من جميع الحسابات وهي جاهزة لإنتاج كشوف الرواتب.</p>
            </div>
        </div>
    </div>
    
    <!-- Wizard Actions -->
    <div class="wizard-actions">
        <button class="btn btn-secondary" id="prev-step" style="display: none;">
            <i class="fas fa-arrow-right ml-2"></i>
            السابق
        </button>
        
        <div class="flex items-center gap-3">
            <button class="btn btn-outline" id="save-draft">
                <i class="fas fa-save ml-2"></i>
                حفظ كمسودة
            </button>
            <button class="btn btn-primary" id="next-step">
                التالي
                <i class="fas fa-arrow-left mr-2"></i>
            </button>
        </div>
    </div>
</div>

<!-- Employee Detail Modal -->
<div class="modal-backdrop hidden" id="employee-detail-modal">
    <div class="modal modal-lg">
        <div class="modal-header">
            <h3 class="modal-title" id="employee-modal-title">تفاصيل راتب الموظف</h3>
            <button type="button" class="modal-close" data-modal-close>&times;</button>
        </div>
        <div class="modal-body">
            <div class="salary-breakdown">
                <div class="breakdown-section">
                    <div class="breakdown-header">
                        <div class="breakdown-icon earnings-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="breakdown-title">الاستحقاقات</div>
                    </div>
                    <div id="earnings-breakdown">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
                
                <div class="breakdown-section">
                    <div class="breakdown-header">
                        <div class="breakdown-icon deductions-icon">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="breakdown-title">الخصومات</div>
                    </div>
                    <div id="deductions-breakdown">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="employee-notes" class="form-label">ملاحظات</label>
                <textarea id="employee-notes" class="form-textarea" rows="3"></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-modal-close>إغلاق</button>
            <button type="button" class="btn btn-primary" id="save-employee-changes">حفظ التغييرات</button>
        </div>
    </div>
</div>
{% endblock %}{% bl
ock extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    let selectedEmployees = [];
    let payrollData = {};
    
    // Initialize wizard
    initializeWizard();
    
    function initializeWizard() {
        // Set current month and year
        const now = new Date();
        document.getElementById('payroll-month').value = now.getMonth() + 1;
        document.getElementById('payroll-year').value = now.getFullYear();
        
        // Load employees for step 2
        loadEmployees();
    }
    
    // Wizard navigation
    document.getElementById('next-step').addEventListener('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < 4) {
                if (currentStep === 1) {
                    loadEmployeesForPeriod();
                } else if (currentStep === 2) {
                    calculatePayroll();
                }
                
                goToStep(currentStep + 1);
            } else {
                generatePayslips();
            }
        }
    });
    
    document.getElementById('prev-step').addEventListener('click', function() {
        if (currentStep > 1) {
            goToStep(currentStep - 1);
        }
    });
    
    // Step navigation
    document.querySelectorAll('.wizard-step').forEach(step => {
        step.addEventListener('click', function() {
            const stepNumber = parseInt(this.getAttribute('data-step'));
            if (stepNumber <= currentStep || this.classList.contains('completed')) {
                goToStep(stepNumber);
            }
        });
    });
    
    function goToStep(step) {
        // Hide current step content
        document.querySelector('.step-content.active').classList.remove('active');
        document.querySelector('.wizard-step.active').classList.remove('active');
        
        // Show new step content
        document.getElementById(`step-${step}`).classList.add('active');
        document.querySelector(`[data-step="${step}"]`).classList.add('active');
        
        // Mark previous steps as completed
        for (let i = 1; i < step; i++) {
            document.querySelector(`[data-step="${i}"]`).classList.add('completed');
        }
        
        // Update navigation buttons
        document.getElementById('prev-step').style.display = step > 1 ? 'block' : 'none';
        
        const nextButton = document.getElementById('next-step');
        if (step === 4) {
            nextButton.innerHTML = '<i class=\"fas fa-file-pdf ml-2\"></i>إنتاج الكشوف';
            nextButton.className = 'btn btn-success';
        } else {
            nextButton.innerHTML = 'التالي <i class=\"fas fa-arrow-left mr-2\"></i>';
            nextButton.className = 'btn btn-primary';
        }
        
        currentStep = step;
    }
    
    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                const month = document.getElementById('payroll-month').value;
                const year = document.getElementById('payroll-year').value;
                const type = document.getElementById('calculation-type').value;
                
                if (!month || !year || !type) {
                    HRComponents.Toast.show('يرجى ملء جميع الحقول المطلوبة', 'error');
                    return false;
                }
                return true;
                
            case 2:
                if (selectedEmployees.length === 0) {
                    HRComponents.Toast.show('يرجى اختيار موظف واحد على الأقل', 'error');
                    return false;
                }
                return true;
                
            case 3:
                return true;
                
            default:
                return true;
        }
    }
    
    function loadEmployees() {
        const departmentId = document.getElementById('department-filter').value;
        
        showLoading();
        
        fetch(`{% url 'Hr:api_get_employees_for_payroll' %}?department=${departmentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderEmployeesGrid(data.employees);
                } else {
                    throw new Error(data.message || 'فشل في تحميل قائمة الموظفين');
                }
            })
            .catch(error => {
                console.error('Error loading employees:', error);
                HRComponents.Toast.show(error.message || 'فشل في تحميل قائمة الموظفين', 'error');
            })
            .finally(() => {
                hideLoading();
            });
    }
    
    function loadEmployeesForPeriod() {
        const month = document.getElementById('payroll-month').value;
        const year = document.getElementById('payroll-year').value;
        const type = document.getElementById('calculation-type').value;
        const departmentId = document.getElementById('department-filter').value;
        
        showLoading();
        
        fetch('{% url "Hr:api_get_employees_for_payroll_period" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                month: month,
                year: year,
                calculation_type: type,
                department_id: departmentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderEmployeesGrid(data.employees);
            } else {
                throw new Error(data.message || 'فشل في تحميل بيانات الموظفين');
            }
        })
        .catch(error => {
            console.error('Error loading employees for period:', error);
            HRComponents.Toast.show(error.message || 'فشل في تحميل بيانات الموظفين', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    function renderEmployeesGrid(employees) {
        const grid = document.getElementById('employees-grid');
        
        grid.innerHTML = employees.map(employee => `
            <div class="employee-card" data-employee-id="${employee.id}" onclick="toggleEmployeeSelection('${employee.id}')">
                <div class="employee-info">
                    <div class="employee-avatar">
                        ${employee.profile_picture ? 
                            `<img src="${employee.profile_picture}" alt="${employee.full_name}" class="w-full h-full object-cover rounded-full">` :
                            `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`
                        }
                    </div>
                    <div class="employee-details">
                        <h4>${employee.full_name}</h4>
                        <div class="employee-meta">
                            ${employee.employee_number} • ${employee.department || 'بدون قسم'}
                        </div>
                    </div>
                </div>
                <div class="employee-salary-info">
                    <span class="text-sm text-neutral-600">الراتب الأساسي</span>
                    <span class="salary-amount">${formatCurrency(employee.basic_salary)}</span>
                </div>
            </div>
        `).join('');
    }
    
    window.toggleEmployeeSelection = function(employeeId) {
        const card = document.querySelector(`[data-employee-id="${employeeId}"]`);
        const index = selectedEmployees.indexOf(employeeId);
        
        if (index > -1) {
            selectedEmployees.splice(index, 1);
            card.classList.remove('selected');
        } else {
            selectedEmployees.push(employeeId);
            card.classList.add('selected');
        }
        
        updateSelectionSummary();
    };
    
    function updateSelectionSummary() {
        const summary = document.getElementById('selection-summary');
        const count = selectedEmployees.length;
        
        if (count > 0) {
            summary.style.display = 'block';
            document.getElementById('selected-count').textContent = count;
            
            // Calculate estimated total (simplified)
            const cards = document.querySelectorAll('.employee-card.selected');
            let estimatedTotal = 0;
            cards.forEach(card => {
                const salaryText = card.querySelector('.salary-amount').textContent;
                const salary = parseFloat(salaryText.replace(/[^0-9.-]+/g, ''));
                estimatedTotal += salary;
            });
            
            document.getElementById('estimated-total').textContent = formatCurrency(estimatedTotal);
            document.getElementById('processing-time').textContent = `${Math.ceil(count / 10)} دقيقة`;
        } else {
            summary.style.display = 'none';
        }
    }
    
    // Select/Clear all employees
    document.getElementById('select-all-employees').addEventListener('click', function() {
        const cards = document.querySelectorAll('.employee-card');
        selectedEmployees = [];
        
        cards.forEach(card => {
            const employeeId = card.getAttribute('data-employee-id');
            selectedEmployees.push(employeeId);
            card.classList.add('selected');
        });
        
        updateSelectionSummary();
    });
    
    document.getElementById('clear-selection').addEventListener('click', function() {
        selectedEmployees = [];
        document.querySelectorAll('.employee-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        updateSelectionSummary();
    });
    
    // Employee search
    document.getElementById('employee-search').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const cards = document.querySelectorAll('.employee-card');
        
        cards.forEach(card => {
            const employeeName = card.querySelector('h4').textContent.toLowerCase();
            const employeeNumber = card.querySelector('.employee-meta').textContent.toLowerCase();
            
            if (employeeName.includes(searchTerm) || employeeNumber.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });
    
    function calculatePayroll() {
        const month = document.getElementById('payroll-month').value;
        const year = document.getElementById('payroll-year').value;
        const type = document.getElementById('calculation-type').value;
        
        showLoading();
        
        fetch('{% url "Hr:api_calculate_payroll" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                month: month,
                year: year,
                calculation_type: type,
                employee_ids: selectedEmployees
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                payrollData = data.payroll_data;
                renderPayrollPreview(data.payroll_data);
                updatePayrollSummary(data.summary);
            } else {
                throw new Error(data.message || 'فشل في حساب الرواتب');
            }
        })
        .catch(error => {
            console.error('Error calculating payroll:', error);
            HRComponents.Toast.show(error.message || 'فشل في حساب الرواتب', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    function renderPayrollPreview(payrollData) {
        const tbody = document.querySelector('#payroll-preview-table tbody');
        
        tbody.innerHTML = payrollData.employees.map(employee => `
            <tr>
                <td>
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                            <span class="text-primary-600 text-sm font-semibold">
                                ${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}
                            </span>
                        </div>
                        <div>
                            <div class="font-medium">${employee.full_name}</div>
                            <div class="text-xs text-neutral-500">${employee.employee_number}</div>
                        </div>
                    </div>
                </td>
                <td class="text-right">${formatCurrency(employee.basic_salary)}</td>
                <td class="text-right">${formatCurrency(employee.allowances)}</td>
                <td class="text-right">${formatCurrency(employee.overtime)}</td>
                <td class="text-right text-success-600 font-semibold">${formatCurrency(employee.total_earnings)}</td>
                <td class="text-right text-error-600">${formatCurrency(employee.total_deductions)}</td>
                <td class="text-right text-primary-600 font-semibold">${formatCurrency(employee.net_salary)}</td>
                <td>
                    <button class="btn btn-primary btn-xs" onclick="showEmployeeDetail('${employee.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    function updatePayrollSummary(summary) {
        document.getElementById('total-earnings').textContent = formatCurrency(summary.total_earnings);
        document.getElementById('total-deductions').textContent = formatCurrency(summary.total_deductions);
        document.getElementById('net-total').textContent = formatCurrency(summary.net_total);
    }
    
    window.showEmployeeDetail = function(employeeId) {
        const employee = payrollData.employees.find(emp => emp.id === employeeId);
        if (!employee) return;
        
        document.getElementById('employee-modal-title').textContent = `تفاصيل راتب ${employee.full_name}`;
        
        // Render earnings breakdown
        const earningsHtml = employee.earnings_breakdown.map(item => `
            <div class="breakdown-item">
                <span class="breakdown-label">${item.label}</span>
                <span class="breakdown-value">${formatCurrency(item.amount)}</span>
            </div>
        `).join('') + `
            <div class="breakdown-item">
                <span class="breakdown-label">إجمالي الاستحقاقات</span>
                <span class="breakdown-value">${formatCurrency(employee.total_earnings)}</span>
            </div>
        `;
        
        // Render deductions breakdown
        const deductionsHtml = employee.deductions_breakdown.map(item => `
            <div class="breakdown-item">
                <span class="breakdown-label">${item.label}</span>
                <span class="breakdown-value">${formatCurrency(item.amount)}</span>
            </div>
        `).join('') + `
            <div class="breakdown-item">
                <span class="breakdown-label">إجمالي الخصومات</span>
                <span class="breakdown-value">${formatCurrency(employee.total_deductions)}</span>
            </div>
        `;
        
        document.getElementById('earnings-breakdown').innerHTML = earningsHtml;
        document.getElementById('deductions-breakdown').innerHTML = deductionsHtml;
        document.getElementById('employee-notes').value = employee.notes || '';
        
        document.getElementById('employee-detail-modal').classList.remove('hidden');
    };
    
    function generatePayslips() {
        const format = document.querySelector('input[name="output_format"]:checked').value;
        const includeBreakdown = document.getElementById('include-breakdown').checked;
        const sendEmail = document.getElementById('send-email').checked;
        
        showLoading();
        
        fetch('{% url "Hr:api_generate_payslips" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                payroll_data: payrollData,
                format: format,
                include_breakdown: includeBreakdown,
                send_email: sendEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show('تم إنتاج كشوف الرواتب بنجاح', 'success');
                
                // Download files
                if (data.download_urls) {
                    data.download_urls.forEach(url => {
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = '';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    });
                }
                
                // Redirect to payroll list after delay
                setTimeout(() => {
                    window.location.href = '{% url "Hr:payroll_list" %}';
                }, 2000);
            } else {
                throw new Error(data.message || 'فشل في إنتاج كشوف الرواتب');
            }
        })
        .catch(error => {
            console.error('Error generating payslips:', error);
            HRComponents.Toast.show(error.message || 'فشل في إنتاج كشوف الرواتب', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    }
    
    // Save draft
    document.getElementById('save-draft').addEventListener('click', function() {
        const draftData = {
            step: currentStep,
            month: document.getElementById('payroll-month').value,
            year: document.getElementById('payroll-year').value,
            calculation_type: document.getElementById('calculation-type').value,
            department_filter: document.getElementById('department-filter').value,
            selected_employees: selectedEmployees,
            payroll_data: payrollData
        };
        
        showLoading();
        
        fetch('{% url "Hr:api_save_payroll_draft" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(draftData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                HRComponents.Toast.show('تم حفظ المسودة بنجاح', 'success');
            } else {
                throw new Error(data.message || 'فشل في حفظ المسودة');
            }
        })
        .catch(error => {
            console.error('Error saving draft:', error);
            HRComponents.Toast.show(error.message || 'فشل في حفظ المسودة', 'error');
        })
        .finally(() => {
            hideLoading();
        });
    });
    
    // Department filter change
    document.getElementById('department-filter').addEventListener('change', function() {
        if (currentStep === 2) {
            loadEmployeesForPeriod();
        }
    });
    
    // Preview payslip
    document.getElementById('preview-payslip').addEventListener('click', function() {
        if (selectedEmployees.length > 0) {
            const firstEmployeeId = selectedEmployees[0];
            window.open(`{% url 'Hr:preview_payslip' 0 %}`.replace('0', firstEmployeeId), '_blank');
        }
    });
    
    // Helper function to format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR',
            minimumFractionDigits: 2
        }).format(amount || 0);
    }
});
</script>
{% endblock %}